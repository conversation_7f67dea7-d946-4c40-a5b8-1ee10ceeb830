[folding]

node_unfolds=[Node<PERSON><PERSON>("."), PackedStringArray("Layout", "Mouse"), NodePath("LeftButton"), PackedStringArray("Transform"), NodePath("LeftButton/Label"), PackedStringArray("Layout"), NodePath("RightButton"), PackedStringArray("Transform"), NodePath("RightButton/Label"), PackedStringArray("Layout"), NodePath("JumpButton"), PackedStringArray("Transform"), NodePath("JumpButton/Label"), PackedStringArray("Layout"), NodePath("AttackButton"), PackedStringArray("Transform"), NodePath("AttackButton/Label"), PackedStringArray("Layout"), NodePath("VirtualJoystick"), PackedStringArray("Visibility", "Layout", "Mouse"), NodePath("VirtualJoystick/Base"), PackedStringArray("Layout", "Mouse"), NodePath("VirtualJoystick/Base/Background"), PackedStringArray("Layout", "Mouse", "Theme Overrides"), NodePath("VirtualJoystick/Knob"), PackedStringArray("Layout", "Mouse"), NodePath("VirtualJoystick/Knob/KnobPanel"), PackedStringArray("Layout", "Mouse", "Theme Overrides")]
resource_unfolds=["res://scenes/ui/TouchControls.tscn::StyleBoxFlat_3", PackedStringArray("Resource", "Border Width", "Border", "Corner Radius"), "res://scenes/ui/TouchControls.tscn::StyleBoxFlat_4", PackedStringArray("Resource", "Border Width", "Border", "Corner Radius")]
nodes_folded=[]
