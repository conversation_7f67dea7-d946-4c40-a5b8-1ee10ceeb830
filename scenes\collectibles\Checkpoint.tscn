[gd_scene load_steps=7 format=3 uid="uid://iqxvn8ywqxqxr"]

[ext_resource type="Script" path="res://scripts/collectibles/Checkpoint.gd" id="1_checkpoint"]
[ext_resource type="Texture2D" uid="uid://bfqhqxqxqxqxr" path="res://assets/sprites/collectibles/save_point_anim_strip_9.png" id="2_checkpoint"]
[ext_resource type="Texture2D" uid="uid://cfqhqxqxqxqxr" path="res://assets/sprites/collectibles/save_point_saving_anim_strip_3.png" id="3_checkpoint"]

[sub_resource type="SpriteFrames" id="SpriteFrames_checkpoint"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": ExtResource("2_checkpoint")
}],
"loop": true,
"name": &"inactive",
"speed": 6.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("3_checkpoint")
}],
"loop": true,
"name": &"active",
"speed": 8.0
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_checkpoint"]
size = Vector2(48, 64)

[sub_resource type="Curve" id="Curve_checkpoint"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]

[sub_resource type="Gradient" id="Gradient_checkpoint"]
colors = PackedColorArray(0.6, 1, 0.6, 1, 0.6, 1, 0.6, 0)

[node name="Checkpoint" type="Area2D"]
collision_layer = 64
collision_mask = 0
script = ExtResource("1_checkpoint")

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
sprite_frames = SubResource("SpriteFrames_checkpoint")
animation = &"inactive"
autoplay = "inactive"

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_checkpoint")

[node name="ActivationParticles" type="CPUParticles2D" parent="."]
emitting = false
amount = 25
lifetime = 1.0
one_shot = true
speed_scale = 1.5
texture = ExtResource("2_checkpoint")
emission_shape = 1
emission_sphere_radius = 16.0
direction = Vector2(0, -1)
spread = 45.0
initial_velocity_min = 50.0
initial_velocity_max = 100.0
gravity = Vector2(0, 150)
scale_amount_min = 0.2
scale_amount_max = 0.5
scale_amount_curve = SubResource("Curve_checkpoint")
color_ramp = SubResource("Gradient_checkpoint")
