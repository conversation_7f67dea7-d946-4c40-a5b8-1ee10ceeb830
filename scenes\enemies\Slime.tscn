[gd_scene load_steps=10 format=3 uid="uid://vj0262n6qu60"]

[ext_resource type="Script" path="res://scripts/enemies/Slime.gd" id="1_slime"]
[ext_resource type="Texture2D" path="res://assets/sprites/enemies/slime/slime_idle_anim_strip_5.png" id="2_slime"]
[ext_resource type="Texture2D" path="res://assets/sprites/enemies/slime/slime_walk_anim_strip_15.png" id="3_slime"]
[ext_resource type="Texture2D" path="res://assets/sprites/enemies/slime/slime_hit_anim_strip_3.png" id="4_slime"]
[ext_resource type="Texture2D" path="res://assets/sprites/enemies/slime/slime_death_anim_strip_6.png" id="5_slime"]

[sub_resource type="SpriteFrames" id="SpriteFrames_slime"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": ExtResource("2_slime")
}],
"loop": true,
"name": &"idle",
"speed": 6.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("3_slime")
}],
"loop": true,
"name": &"walk",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("4_slime")
}],
"loop": false,
"name": &"hit",
"speed": 8.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("5_slime")
}],
"loop": false,
"name": &"death",
"speed": 6.0
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_slime"]
size = Vector2(32, 24)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_detector"]
size = Vector2(80, 40)

[sub_resource type="Gradient" id="Gradient_slime"]
colors = PackedColorArray(0.8, 1, 0.8, 1, 0.8, 1, 0.8, 0)

[node name="Slime" type="CharacterBody2D"]
collision_layer = 2
collision_mask = 20
script = ExtResource("1_slime")

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
sprite_frames = SubResource("SpriteFrames_slime")
animation = &"idle"
autoplay = "idle"

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_slime")

[node name="PlayerDetector" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 1

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
shape = SubResource("RectangleShape2D_detector")

[node name="WallDetector" type="RayCast2D" parent="."]
target_position = Vector2(20, 0)
collision_mask = 20

[node name="FloorDetector" type="RayCast2D" parent="."]
position = Vector2(16, 0)
target_position = Vector2(0, 20)
collision_mask = 20

[node name="HitParticles" type="CPUParticles2D" parent="."]
emitting = false
amount = 10
lifetime = 0.5
one_shot = true
speed_scale = 1.5
texture = ExtResource("2_slime")
emission_shape = 1
emission_sphere_radius = 8.0
direction = Vector2(0, -1)
spread = 30.0
initial_velocity_min = 40.0
initial_velocity_max = 80.0
gravity = Vector2(0, 150)
scale_amount_min = 0.2
scale_amount_max = 0.4
color_ramp = SubResource("Gradient_slime")

[node name="DeathTimer" type="Timer" parent="."]
wait_time = 1.0
one_shot = true

[node name="DamageArea" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 1

[node name="CollisionShape2D" type="CollisionShape2D" parent="DamageArea"]
shape = SubResource("RectangleShape2D_slime")

[connection signal="body_entered" from="DamageArea" to="." method="_on_body_entered"]
