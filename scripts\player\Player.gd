extends CharacterBody2D
class_name Player

# Player movement and state management
signal health_changed(new_health: int)
signal died
signal landed
signal jumped
signal double_jumped
signal attacked
signal coin_collected(amount: int)

# Movement constants
const SPEED = 300.0
const JUMP_VELOCITY = -600.0
const DOUBLE_JUMP_VELOCITY = -500.0
const ACCELERATION = 1500.0
const FRICTION = 1200.0
const WALL_JUMP_VELOCITY = Vector2(400, -500)

# State variables
var max_health: int = 3
var current_health: int = 3
var can_double_jump: bool = true
var has_double_jumped: bool = false
var is_attacking: bool = false
var is_invulnerable: bool = false
var invulnerability_time: float = 1.5

# Input handling
var input_buffer_time: float = 0.1
var jump_buffer_timer: float = 0.0
var coyote_time: float = 0.15
var coyote_timer: float = 0.0

# References
@onready var sprite: AnimatedSprite2D = $AnimatedSprite2D
@onready var collision_shape: CollisionShape2D = $CollisionShape2D
@onready var attack_area: Area2D = $AttackArea
@onready var attack_collision: CollisionShape2D = $AttackArea/CollisionShape2D
@onready var dust_particles: CPUParticles2D = $DustParticles
@onready var invulnerability_timer: Timer = $InvulnerabilityTimer

# Animation state
enum AnimationState {
	IDLE,
	RUN,
	JUMP_UP,
	JUMP_DOWN,
	DOUBLE_JUMP,
	ATTACK,
	HIT,
	DEATH
}

var current_animation_state: AnimationState = AnimationState.IDLE
var facing_direction: int = 1

func _ready():
	# Connect signals
	attack_area.body_entered.connect(_on_enemy_hit)
	invulnerability_timer.timeout.connect(_on_invulnerability_ended)
	invulnerability_timer.wait_time = invulnerability_time
	
	# Set up collision layers
	collision_layer = 1  # Player layer
	collision_mask = 4 | 8 | 16 | 32  # Environment, Collectibles, Platforms, Hazards
	
	# Set up attack area
	attack_area.collision_layer = 0
	attack_area.collision_mask = 2  # Enemies layer
	attack_collision.disabled = true
	
	# Initialize health
	current_health = max_health
	
	# Connect to GameManager
	if GameManager:
		GameManager.player_died.connect(_on_game_manager_player_died)

func _physics_process(delta):
	handle_input(delta)
	apply_gravity(delta)
	handle_movement(delta)
	update_animation_state()
	move_and_slide()
	
	# Check if landed
	if is_on_floor() and velocity.y >= 0:
		if has_double_jumped or coyote_timer <= 0:
			_on_landed()

func handle_input(delta):
	# Jump input buffering
	if Input.is_action_just_pressed("jump"):
		jump_buffer_timer = input_buffer_time
	
	jump_buffer_timer -= delta
	coyote_timer -= delta
	
	# Handle jump
	if jump_buffer_timer > 0:
		if is_on_floor() or coyote_timer > 0:
			jump()
			jump_buffer_timer = 0
		elif can_double_jump and not has_double_jumped:
			double_jump()
			jump_buffer_timer = 0
	
	# Handle attack
	if Input.is_action_just_pressed("attack") and not is_attacking:
		attack()

func apply_gravity(delta):
	if not is_on_floor():
		velocity.y += get_gravity().y * delta
		
		# Variable jump height
		if velocity.y < 0 and not Input.is_action_pressed("jump"):
			velocity.y += get_gravity().y * delta * 0.5

func handle_movement(delta):
	var input_direction = Input.get_axis("move_left", "move_right")
	
	if input_direction != 0:
		facing_direction = sign(input_direction)
		sprite.flip_h = facing_direction < 0
		
		# Accelerate
		velocity.x = move_toward(velocity.x, input_direction * SPEED, ACCELERATION * delta)
	else:
		# Apply friction
		velocity.x = move_toward(velocity.x, 0, FRICTION * delta)

func jump():
	velocity.y = JUMP_VELOCITY
	coyote_timer = 0
	jumped.emit()
	
	if AudioManager:
		AudioManager.play_jump_sound()
	
	# Trigger haptic feedback
	if UIManager:
		UIManager.trigger_haptic_feedback(0.3)
	
	# Spawn dust particles
	spawn_dust_effect()

func double_jump():
	velocity.y = DOUBLE_JUMP_VELOCITY
	has_double_jumped = true
	double_jumped.emit()
	
	if AudioManager:
		AudioManager.play_jump_sound()
	
	# Trigger haptic feedback
	if UIManager:
		UIManager.trigger_haptic_feedback(0.5)

func attack():
	if is_attacking:
		return
	
	is_attacking = true
	attack_collision.disabled = false
	attacked.emit()
	
	if AudioManager:
		AudioManager.play_sfx("sword_swing", 0.1)
	
	# Trigger haptic feedback
	if UIManager:
		UIManager.trigger_haptic_feedback(0.4)
	
	# Attack duration
	await get_tree().create_timer(0.3).timeout
	attack_collision.disabled = true
	is_attacking = false

func take_damage(damage: int = 1):
	if is_invulnerable:
		return
	
	current_health -= damage
	health_changed.emit(current_health)
	
	if AudioManager:
		AudioManager.play_player_hurt_sound()
	
	# Trigger haptic feedback
	if UIManager:
		UIManager.trigger_haptic_feedback(0.8)
	
	if current_health <= 0:
		die()
	else:
		start_invulnerability()

func heal(amount: int = 1):
	current_health = min(max_health, current_health + amount)
	health_changed.emit(current_health)
	
	if AudioManager:
		AudioManager.play_power_up_sound()

func die():
	died.emit()
	
	if AudioManager:
		AudioManager.play_sfx("player_death")
	
	# Death animation and logic
	set_physics_process(false)
	current_animation_state = AnimationState.DEATH

func start_invulnerability():
	is_invulnerable = true
	invulnerability_timer.start()
	
	# Visual feedback - flashing effect
	var tween = create_tween()
	tween.set_loops(int(invulnerability_time * 5))
	tween.tween_property(sprite, "modulate:a", 0.3, 0.1)
	tween.tween_property(sprite, "modulate:a", 1.0, 0.1)

func _on_invulnerability_ended():
	is_invulnerable = false
	sprite.modulate.a = 1.0

func _on_landed():
	has_double_jumped = false
	coyote_timer = coyote_time
	landed.emit()
	
	if AudioManager:
		AudioManager.play_land_sound()
	
	spawn_dust_effect()

func _on_enemy_hit(body):
	if body.has_method("take_damage"):
		body.take_damage(1)

func spawn_dust_effect():
	if dust_particles:
		dust_particles.emitting = true

func update_animation_state():
	var new_state = current_animation_state
	
	if current_health <= 0:
		new_state = AnimationState.DEATH
	elif is_attacking:
		new_state = AnimationState.ATTACK
	elif not is_on_floor():
		if has_double_jumped:
			new_state = AnimationState.DOUBLE_JUMP
		elif velocity.y < 0:
			new_state = AnimationState.JUMP_UP
		else:
			new_state = AnimationState.JUMP_DOWN
	elif abs(velocity.x) > 10:
		new_state = AnimationState.RUN
	else:
		new_state = AnimationState.IDLE
	
	if new_state != current_animation_state:
		current_animation_state = new_state
		play_animation(new_state)

func play_animation(state: AnimationState):
	match state:
		AnimationState.IDLE:
			sprite.play("idle")
		AnimationState.RUN:
			sprite.play("run")
		AnimationState.JUMP_UP:
			sprite.play("jump_up")
		AnimationState.JUMP_DOWN:
			sprite.play("jump_down")
		AnimationState.DOUBLE_JUMP:
			sprite.play("double_jump")
		AnimationState.ATTACK:
			sprite.play("attack")
		AnimationState.HIT:
			sprite.play("hit")
		AnimationState.DEATH:
			sprite.play("death")

func _on_game_manager_player_died():
	# Reset player state for respawn
	current_health = max_health
	is_invulnerable = false
	is_attacking = false
	has_double_jumped = false
	set_physics_process(true)
	sprite.modulate.a = 1.0
	
	# Reset position to checkpoint
	if GameManager.current_checkpoint != Vector2.ZERO:
		global_position = GameManager.current_checkpoint

func collect_coin(amount: int = 1):
	coin_collected.emit(amount)
	
	if GameManager:
		GameManager.add_coins(amount)
	
	if AudioManager:
		AudioManager.play_coin_collect_sound()
