[folding]

node_unfolds=[NodePath("."), PackedStringArray("Transform"), NodePath("Background"), PackedStringArray("Layout"), NodePath("Player"), PackedStringArray("Transform"), NodePath("UI/GameHUD"), PackedStringArray("Layout"), NodePath("Platforms"), PackedStringArray("Collision"), NodePath("Platforms/Ground"), PackedStringArray("Transform"), NodePath("Platforms/Platform1"), PackedStringArray("Transform"), NodePath("Platforms/Platform2"), PackedStringArray("Transform"), NodePath("Platforms/Platform3"), PackedStringArray("Transform"), NodePath("Collectibles/Coin1"), PackedStringArray("Transform"), NodePath("Collectibles/Coin2"), PackedStringArray("Transform"), NodePath("Collectibles/Coin3"), PackedStringArray("Transform"), NodePath("Enemies/Slime1"), PackedStringArray("Transform"), NodePath("LevelExit"), PackedStringArray("Transform", "Collision"), NodePath("LevelExit/ExitSign"), PackedStringArray("Layout")]
resource_unfolds=["res://scenes/ui/GameHUD.tscn::Theme_hud", PackedStringArray("Resource"), "res://scenes/levels/TestLevel.tscn::RectangleShape2D_exit", PackedStringArray("Resource")]
nodes_folded=[]
