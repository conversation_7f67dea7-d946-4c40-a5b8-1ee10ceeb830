extends Node2D
class_name Level

# Base level class with common functionality
signal level_completed
signal player_died
signal checkpoint_reached(position: Vector2)

@export var level_name: String = "Level 1"
@export var level_number: int = 1
@export var time_limit: float = 0.0  # 0 = no time limit
@export var target_coins: int = 0     # 0 = no coin requirement

@onready var player: Player = $Player
@onready var hud: GameHUD = $UI/GameHUD
@onready var level_exit: Area2D = $LevelExit
@onready var background: ParallaxBackground = $ParallaxBackground
@onready var tilemap: TileMap = $TileMap

# Level state
var level_start_time: float
var coins_collected: int = 0
var enemies_defeated: int = 0
var checkpoints_activated: Array[Vector2] = []
var is_level_complete: bool = false

# Collections
var coins: Array[Coin] = []
var enemies: Array = []
var checkpoints: Array = []

func _ready():
	# Initialize level
	setup_level()
	connect_signals()
	start_level()

func setup_level():
	# Find and register all collectibles and enemies
	find_level_objects()
	
	# Set up player
	if player:
		player.global_position = get_spawn_position()
	
	# Set up level exit
	if level_exit:
		level_exit.body_entered.connect(_on_level_exit_entered)
	
	# Set up camera limits based on tilemap
	setup_camera_limits()

func find_level_objects():
	# Find all coins
	coins = find_children("*", "Coin", true, false)
	for coin in coins:
		if coin.has_signal("collected"):
			coin.collected.connect(_on_coin_collected)
	
	# Find all enemies
	var enemy_groups = ["Slime", "Goblin", "Fly", "Mushroom", "Worm"]
	for group in enemy_groups:
		var found_enemies = find_children("*", group, true, false)
		enemies.append_array(found_enemies)
		for enemy in found_enemies:
			if enemy.has_signal("died"):
				enemy.died.connect(_on_enemy_died)
	
	# Find all checkpoints
	checkpoints = find_children("*", "Checkpoint", true, false)
	for checkpoint in checkpoints:
		if checkpoint.has_signal("activated"):
			checkpoint.activated.connect(_on_checkpoint_activated)

func connect_signals():
	# Connect player signals
	if player:
		player.died.connect(_on_player_died)
		player.coin_collected.connect(_on_player_coin_collected)
	
	# Connect to GameManager
	if GameManager:
		GameManager.game_paused.connect(_on_game_paused)
		GameManager.game_resumed.connect(_on_game_resumed)

func start_level():
	level_start_time = Time.get_time_dict_from_system()["unix"]
	
	# Show level info
	if hud:
		hud.show_level_info(level_name, level_number)
	
	# Start background music
	if AudioManager:
		AudioManager.play_level_music()
	
	# Initialize GameManager state
	if GameManager:
		GameManager.start_game(level_number)

func get_spawn_position() -> Vector2:
	# Look for spawn point, otherwise use default
	var spawn_point = find_child("SpawnPoint")
	if spawn_point:
		return spawn_point.global_position
	return Vector2(100, 400)  # Default spawn position

func setup_camera_limits():
	if not player or not tilemap:
		return
	
	var camera = player.get_node("Camera2D")
	if not camera:
		return
	
	# Get tilemap bounds
	var used_rect = tilemap.get_used_rect()
	var tile_size = tilemap.tile_set.tile_size
	
	# Set camera limits
	camera.limit_left = used_rect.position.x * tile_size.x
	camera.limit_top = used_rect.position.y * tile_size.y
	camera.limit_right = (used_rect.position.x + used_rect.size.x) * tile_size.x
	camera.limit_bottom = (used_rect.position.y + used_rect.size.y) * tile_size.y

func _on_coin_collected(value: int):
	coins_collected += value
	
	# Check if all required coins collected
	if target_coins > 0 and coins_collected >= target_coins:
		# Could trigger something special
		pass

func _on_player_coin_collected(amount: int):
	# Additional coin collection handling
	pass

func _on_enemy_died():
	enemies_defeated += 1
	
	# Award bonus points for defeating all enemies
	var remaining_enemies = 0
	for enemy in enemies:
		if enemy and is_instance_valid(enemy) and not enemy.is_dead:
			remaining_enemies += 1
	
	if remaining_enemies == 0:
		# All enemies defeated bonus
		if GameManager:
			GameManager.add_score(500)

func _on_checkpoint_activated(position: Vector2):
	if position not in checkpoints_activated:
		checkpoints_activated.append(position)
		
		if GameManager:
			GameManager.set_checkpoint(position)
		
		checkpoint_reached.emit(position)
		
		if AudioManager:
			AudioManager.play_checkpoint_sound()

func _on_level_exit_entered(body):
	if body is Player and not is_level_complete:
		complete_level()

func complete_level():
	if is_level_complete:
		return
	
	is_level_complete = true
	
	# Calculate completion stats
	var completion_time = Time.get_time_dict_from_system()["unix"] - level_start_time
	var total_coins = coins.size()
	var coin_percentage = (coins_collected * 100) / max(total_coins, 1)
	
	# Award completion bonuses
	if GameManager:
		# Time bonus
		if time_limit > 0 and completion_time < time_limit:
			var time_bonus = int((time_limit - completion_time) * 10)
			GameManager.add_score(time_bonus)
		
		# Coin collection bonus
		if coin_percentage >= 100:
			GameManager.add_score(1000)  # Perfect coin collection
		elif coin_percentage >= 80:
			GameManager.add_score(500)   # Good coin collection
		
		# No damage bonus
		if player and player.current_health == player.max_health:
			GameManager.add_score(500)
		
		# Save level progress
		if SaveManager:
			SaveManager.save_level_progress(level_number, GameManager.score, coins_collected, completion_time)
		
		GameManager.complete_level()
	
	# Play completion sound
	if AudioManager:
		AudioManager.play_level_complete_sound()
	
	level_completed.emit()

func _on_player_died():
	player_died.emit()

func _on_game_paused():
	# Pause level-specific elements
	set_process_mode(Node.PROCESS_MODE_WHEN_PAUSED)

func _on_game_resumed():
	# Resume level-specific elements
	set_process_mode(Node.PROCESS_MODE_INHERIT)

func restart_level():
	# Reset level state
	is_level_complete = false
	coins_collected = 0
	enemies_defeated = 0
	checkpoints_activated.clear()
	
	# Reset player
	if player:
		player.global_position = get_spawn_position()
		player.current_health = player.max_health
	
	# Reset GameManager state
	if GameManager:
		GameManager.reset_level_stats()
	
	# Restart level timer
	level_start_time = Time.get_time_dict_from_system()["unix"]

func get_level_stats() -> Dictionary:
	var completion_time = Time.get_time_dict_from_system()["unix"] - level_start_time
	return {
		"level_name": level_name,
		"level_number": level_number,
		"completion_time": completion_time,
		"coins_collected": coins_collected,
		"total_coins": coins.size(),
		"enemies_defeated": enemies_defeated,
		"total_enemies": enemies.size(),
		"checkpoints_reached": checkpoints_activated.size(),
		"total_checkpoints": checkpoints.size()
	}
