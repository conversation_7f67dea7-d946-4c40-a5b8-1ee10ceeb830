[gd_scene load_steps=6 format=3 uid="uid://jqxvn8ywqxqxr"]

[ext_resource type="Script" path="res://scripts/collectibles/HealthPotion.gd" id="1_potion"]
[ext_resource type="Texture2D" uid="uid://bfqhqxqxqxqxr" path="res://assets/sprites/collectibles/health_potion.png" id="2_potion"]

[sub_resource type="CircleShape2D" id="CircleShape2D_potion"]
radius = 16.0

[sub_resource type="Curve" id="Curve_potion"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]

[sub_resource type="Gradient" id="Gradient_potion"]
colors = PackedColorArray(1, 0.6, 0.6, 1, 1, 0.6, 0.6, 0)

[node name="HealthPotion" type="Area2D"]
collision_layer = 8
collision_mask = 0
script = ExtResource("1_potion")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_potion")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_potion")

[node name="CollectParticles" type="CPUParticles2D" parent="."]
emitting = false
amount = 20
lifetime = 1.0
one_shot = true
speed_scale = 1.5
texture = ExtResource("2_potion")
emission_shape = 1
emission_sphere_radius = 12.0
direction = Vector2(0, -1)
spread = 45.0
initial_velocity_min = 40.0
initial_velocity_max = 80.0
gravity = Vector2(0, 120)
scale_amount_min = 0.3
scale_amount_max = 0.6
scale_amount_curve = SubResource("Curve_potion")
color_ramp = SubResource("Gradient_potion")
