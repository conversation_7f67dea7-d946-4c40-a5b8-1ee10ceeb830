[gd_scene load_steps=6 format=3 uid="uid://fqxvn8ywqxqxr"]

[ext_resource type="Script" path="res://scripts/ui/GameHUD.gd" id="1_hud"]
[ext_resource type="PackedScene" uid="uid://cqxvn8ywqxqxr" path="res://scenes/ui/TouchControls.tscn" id="2_touch"]
[ext_resource type="Texture2D" uid="uid://bfqhqxqxqxqxr" path="res://assets/sprites/ui/coins_hud.png" id="3_coins"]
[ext_resource type="Texture2D" uid="uid://cfqhqxqxqxqxr" path="res://assets/sprites/ui/lifes_icon.png" id="4_lives"]

[sub_resource type="Theme" id="Theme_hud"]
default_font_size = 16

[node name="GameHUD" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
theme = SubResource("Theme_hud")
script = ExtResource("1_hud")

[node name="TopBar" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 60.0

[node name="LeftSide" type="HBoxContainer" parent="TopBar"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HealthContainer" type="HBoxContainer" parent="TopBar/LeftSide"]
layout_mode = 2
size_flags_vertical = 4

[node name="CoinsContainer" type="HBoxContainer" parent="TopBar/LeftSide"]
layout_mode = 2
size_flags_vertical = 4

[node name="CoinIcon" type="TextureRect" parent="TopBar/LeftSide/CoinsContainer"]
layout_mode = 2
size_flags_vertical = 4
texture = ExtResource("3_coins")
stretch_mode = 4

[node name="CoinsLabel" type="Label" parent="TopBar/LeftSide/CoinsContainer"]
layout_mode = 2
size_flags_vertical = 4
text = "0"
vertical_alignment = 1

[node name="RightSide" type="HBoxContainer" parent="TopBar"]
layout_mode = 2
size_flags_horizontal = 10

[node name="ScoreContainer" type="HBoxContainer" parent="TopBar/RightSide"]
layout_mode = 2
size_flags_vertical = 4

[node name="ScoreLabel2" type="Label" parent="TopBar/RightSide/ScoreContainer"]
layout_mode = 2
size_flags_vertical = 4
text = "Score: "
vertical_alignment = 1

[node name="ScoreLabel" type="Label" parent="TopBar/RightSide/ScoreContainer"]
layout_mode = 2
size_flags_vertical = 4
text = "0"
vertical_alignment = 1

[node name="LivesContainer" type="HBoxContainer" parent="TopBar/RightSide"]
layout_mode = 2
size_flags_vertical = 4

[node name="LifeIcon" type="TextureRect" parent="TopBar/RightSide/LivesContainer"]
layout_mode = 2
size_flags_vertical = 4
texture = ExtResource("4_lives")
stretch_mode = 4

[node name="LivesLabel" type="Label" parent="TopBar/RightSide/LivesContainer"]
layout_mode = 2
size_flags_vertical = 4
text = "x3"
vertical_alignment = 1

[node name="PauseButton" type="Button" parent="TopBar/RightSide"]
layout_mode = 2
size_flags_vertical = 4
text = "PAUSE"

[node name="TouchControls" parent="." instance=ExtResource("2_touch")]
layout_mode = 1
