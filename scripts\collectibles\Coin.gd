extends Area2D
class_name Coin

# Coin collectible with animation and effects
signal collected(value: int)

@export var coin_value: int = 1
@export var bounce_height: float = 10.0
@export var bounce_speed: float = 2.0

@onready var sprite: AnimatedSprite2D = $AnimatedSprite2D
@onready var collision_shape: CollisionShape2D = $CollisionShape2D
@onready var collect_particles: CPUParticles2D = $CollectParticles
@onready var audio_player: AudioStreamPlayer2D = $AudioStreamPlayer2D

var is_collected: bool = false
var original_position: Vector2
var bounce_timer: float = 0.0

func _ready():
	# Set up collision layers
	collision_layer = 8  # Collectibles layer
	collision_mask = 0   # Don't collide with anything
	
	# Connect signals
	body_entered.connect(_on_body_entered)
	
	# Store original position for bouncing animation
	original_position = global_position
	
	# Start idle animation
	if sprite:
		sprite.play("idle")
	
	# Set up particles
	if collect_particles:
		collect_particles.emitting = false

func _physics_process(delta):
	if not is_collected:
		# Gentle bouncing animation
		bounce_timer += delta * bounce_speed
		global_position.y = original_position.y + sin(bounce_timer) * bounce_height

func _on_body_entered(body):
	if is_collected:
		return
	
	if body is Player:
		collect_coin(body)

func collect_coin(player: Player):
	if is_collected:
		return
	
	is_collected = true
	
	# Disable collision
	collision_shape.disabled = true
	
	# Play collection animation
	if sprite:
		sprite.play("collect")
	
	# Emit particles
	if collect_particles:
		collect_particles.emitting = true
	
	# Play sound effect
	if AudioManager:
		AudioManager.play_coin_collect_sound()
	
	# Trigger haptic feedback
	if UIManager:
		UIManager.trigger_haptic_feedback(0.2)
	
	# Add to player's coin count
	if player.has_method("collect_coin"):
		player.collect_coin(coin_value)
	
	# Update game manager
	if GameManager:
		GameManager.add_coins(coin_value)
		GameManager.add_score(coin_value * 10)
	
	# Emit signal
	collected.emit(coin_value)
	
	# Animate collection effect
	animate_collection()

func animate_collection():
	# Scale up and fade out animation
	var tween = create_tween()
	tween.set_parallel(true)
	
	# Scale animation
	tween.tween_property(self, "scale", Vector2(1.5, 1.5), 0.3)
	
	# Fade out animation
	tween.tween_property(self, "modulate:a", 0.0, 0.3)
	
	# Move up slightly
	tween.tween_property(self, "global_position:y", global_position.y - 30, 0.3)
	
	# Wait for animation to complete
	await tween.finished
	
	# Remove the coin
	queue_free()

func set_coin_value(value: int):
	coin_value = value
	
	# You could change the coin appearance based on value
	match value:
		1:
			# Bronze coin
			modulate = Color.WHITE
		5:
			# Silver coin
			modulate = Color(0.8, 0.8, 1.0)
		10:
			# Gold coin
			modulate = Color(1.0, 1.0, 0.6)

# For save/load functionality
func get_save_data() -> Dictionary:
	return {
		"position": global_position,
		"collected": is_collected,
		"value": coin_value
	}

func load_save_data(data: Dictionary):
	if data.has("position"):
		global_position = data.position
		original_position = global_position
	
	if data.has("collected") and data.collected:
		# If already collected, remove immediately
		queue_free()
	
	if data.has("value"):
		set_coin_value(data.value)
