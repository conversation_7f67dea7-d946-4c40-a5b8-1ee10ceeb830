[gd_scene load_steps=8 format=3 uid="uid://dqxvn8ywqxqxr"]

[ext_resource type="Script" path="res://scripts/collectibles/Coin.gd" id="1_coin"]
[ext_resource type="Texture2D" path="res://assets/sprites/collectibles/coin_anim_strip_6.png" id="2_coin"]
[ext_resource type="Texture2D" path="res://assets/sprites/collectibles/coin_pickup_anim_strip_6.png" id="3_coin"]

[sub_resource type="SpriteFrames" id="SpriteFrames_coin"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": ExtResource("2_coin")
}],
"loop": true,
"name": &"idle",
"speed": 8.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("3_coin")
}],
"loop": false,
"name": &"collect",
"speed": 12.0
}]

[sub_resource type="CircleShape2D" id="CircleShape2D_coin"]
radius = 16.0

[sub_resource type="Curve" id="Curve_coin"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]

[sub_resource type="Gradient" id="Gradient_coin"]
colors = PackedColorArray(1, 1, 0.6, 1, 1, 1, 0.6, 0)

[node name="Coin" type="Area2D"]
collision_layer = 8
collision_mask = 0
script = ExtResource("1_coin")

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
sprite_frames = SubResource("SpriteFrames_coin")
animation = &"idle"
autoplay = "idle"

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_coin")

[node name="CollectParticles" type="CPUParticles2D" parent="."]
emitting = false
amount = 15
lifetime = 0.8
one_shot = true
speed_scale = 1.5
texture = ExtResource("2_coin")
emission_shape = 1
emission_sphere_radius = 8.0
direction = Vector2(0, -1)
spread = 45.0
initial_velocity_min = 30.0
initial_velocity_max = 80.0
gravity = Vector2(0, 100)
scale_amount_min = 0.2
scale_amount_max = 0.5
scale_amount_curve = SubResource("Curve_coin")
color_ramp = SubResource("Gradient_coin")

[node name="AudioStreamPlayer2D" type="AudioStreamPlayer2D" parent="."]
max_distance = 500.0
