[gd_scene load_steps=15 format=3 uid="uid://bqxvn8ywqxqxr"]

[ext_resource type="Script" path="res://scripts/player/Player.gd" id="1_0k8vx"]
[ext_resource type="Texture2D" path="res://assets/sprites/player/herochar_idle_anim_strip_4.png" id="2_1k8vx"]
[ext_resource type="Texture2D" path="res://assets/sprites/player/herochar_run_anim_strip_6.png" id="3_2k8vx"]
[ext_resource type="Texture2D" path="res://assets/sprites/player/herochar_jump_up_anim_strip_3.png" id="4_3k8vx"]
[ext_resource type="Texture2D" path="res://assets/sprites/player/herochar_jump_down_anim_strip_3.png" id="5_4k8vx"]
[ext_resource type="Texture2D" path="res://assets/sprites/player/herochar_jump_double_anim_strip_3.png" id="6_5k8vx"]
[ext_resource type="Texture2D" path="res://assets/sprites/player/herochar_attack_anim_strip_4(new).png" id="7_6k8vx"]
[ext_resource type="Texture2D" path="res://assets/sprites/player/herochar_hit_anim_strip_3.png" id="8_7k8vx"]
[ext_resource type="Texture2D" path="res://assets/sprites/player/herochar_death_anim_strip_8.png" id="9_8k8vx"]

[sub_resource type="SpriteFrames" id="SpriteFrames_1"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": ExtResource("2_1k8vx")
}],
"loop": true,
"name": &"idle",
"speed": 8.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("3_2k8vx")
}],
"loop": true,
"name": &"run",
"speed": 12.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("4_3k8vx")
}],
"loop": false,
"name": &"jump_up",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("5_4k8vx")
}],
"loop": false,
"name": &"jump_down",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("6_5k8vx")
}],
"loop": false,
"name": &"double_jump",
"speed": 12.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("7_6k8vx")
}],
"loop": false,
"name": &"attack",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("8_7k8vx")
}],
"loop": false,
"name": &"hit",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("9_8k8vx")
}],
"loop": false,
"name": &"death",
"speed": 8.0
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(32, 48)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2"]
size = Vector2(40, 32)

[sub_resource type="Curve" id="Curve_1"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]

[sub_resource type="Gradient" id="Gradient_1"]
colors = PackedColorArray(1, 1, 1, 1, 1, 1, 1, 0)

[node name="Player" type="CharacterBody2D"]
collision_layer = 1
collision_mask = 60
script = ExtResource("1_0k8vx")

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
sprite_frames = SubResource("SpriteFrames_1")
animation = &"idle"
autoplay = "idle"

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")

[node name="AttackArea" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 2

[node name="CollisionShape2D" type="CollisionShape2D" parent="AttackArea"]
position = Vector2(24, 0)
shape = SubResource("RectangleShape2D_2")
disabled = true

[node name="DustParticles" type="CPUParticles2D" parent="."]
position = Vector2(0, 24)
emitting = false
amount = 20
lifetime = 0.5
one_shot = true
speed_scale = 2.0
texture = ExtResource("2_1k8vx")
emission_shape = 3
emission_rect_extents = Vector2(16, 4)
direction = Vector2(0, -1)
spread = 30.0
initial_velocity_min = 50.0
initial_velocity_max = 100.0
gravity = Vector2(0, 200)
scale_amount_min = 0.1
scale_amount_max = 0.3
scale_amount_curve = SubResource("Curve_1")
color_ramp = SubResource("Gradient_1")

[node name="InvulnerabilityTimer" type="Timer" parent="."]
wait_time = 1.5
one_shot = true

[node name="Camera2D" type="Camera2D" parent="."]
limit_left = 0
limit_top = 0
limit_right = 2000
limit_bottom = 1200
limit_smoothed = true
position_smoothing_enabled = true
position_smoothing_speed = 8.0
drag_horizontal_enabled = true
drag_vertical_enabled = true
drag_left_margin = 0.3
drag_top_margin = 0.3
drag_right_margin = 0.3
drag_bottom_margin = 0.3
