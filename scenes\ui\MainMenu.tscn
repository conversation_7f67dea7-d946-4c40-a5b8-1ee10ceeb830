[gd_scene load_steps=4 format=3 uid="uid://gqxvn8ywqxqxr"]

[ext_resource type="Script" path="res://scripts/ui/MainMenu.gd" id="1_menu"]
[ext_resource type="Texture2D" uid="uid://bfqhqxqxqxqxr" path="res://assets/sprites/environment/background.png" id="2_bg"]

[sub_resource type="Theme" id="Theme_menu"]
default_font_size = 20

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme = SubResource("Theme_menu")
script = ExtResource("1_menu")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
stretch_mode = 1

[node name="VBox" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -200.0
offset_right = 150.0
offset_bottom = 200.0

[node name="TitleLabel" type="Label" parent="VBox"]
layout_mode = 2
text = "MOBILE PLATFORMER"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer" type="Control" parent="VBox"]
layout_mode = 2
size_flags_vertical = 3

[node name="ButtonContainer" type="VBoxContainer" parent="VBox"]
layout_mode = 2
size_flags_vertical = 3

[node name="PlayButton" type="Button" parent="VBox/ButtonContainer"]
layout_mode = 2
text = "PLAY"

[node name="LevelSelectButton" type="Button" parent="VBox/ButtonContainer"]
layout_mode = 2
text = "LEVEL SELECT"

[node name="SettingsButton" type="Button" parent="VBox/ButtonContainer"]
layout_mode = 2
text = "SETTINGS"

[node name="QuitButton" type="Button" parent="VBox/ButtonContainer"]
layout_mode = 2
text = "QUIT"

[node name="LevelSelectPanel" type="Control" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="VBox" type="VBoxContainer" parent="LevelSelectPanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -250.0
offset_right = 200.0
offset_bottom = 250.0

[node name="TitleLabel" type="Label" parent="LevelSelectPanel/VBox"]
layout_mode = 2
text = "SELECT LEVEL"
horizontal_alignment = 1

[node name="ScrollContainer" type="ScrollContainer" parent="LevelSelectPanel/VBox"]
layout_mode = 2
size_flags_vertical = 3

[node name="LevelGrid" type="GridContainer" parent="LevelSelectPanel/VBox/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
columns = 5

[node name="BackButton" type="Button" parent="LevelSelectPanel/VBox"]
layout_mode = 2
text = "BACK"

[node name="SettingsPanel" type="Control" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="VBox" type="VBoxContainer" parent="SettingsPanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -200.0
offset_right = 200.0
offset_bottom = 200.0

[node name="TitleLabel" type="Label" parent="SettingsPanel/VBox"]
layout_mode = 2
text = "SETTINGS"
horizontal_alignment = 1

[node name="MusicContainer" type="HBoxContainer" parent="SettingsPanel/VBox"]
layout_mode = 2

[node name="MusicLabel" type="Label" parent="SettingsPanel/VBox/MusicContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Music Volume:"
vertical_alignment = 1

[node name="MusicSlider" type="HSlider" parent="SettingsPanel/VBox/MusicContainer"]
layout_mode = 2
size_flags_horizontal = 3
max_value = 100.0
step = 1.0
value = 70.0

[node name="SFXContainer" type="HBoxContainer" parent="SettingsPanel/VBox"]
layout_mode = 2

[node name="SFXLabel" type="Label" parent="SettingsPanel/VBox/SFXContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "SFX Volume:"
vertical_alignment = 1

[node name="SFXSlider" type="HSlider" parent="SettingsPanel/VBox/SFXContainer"]
layout_mode = 2
size_flags_horizontal = 3
max_value = 100.0
step = 1.0
value = 80.0

[node name="HapticContainer" type="HBoxContainer" parent="SettingsPanel/VBox"]
layout_mode = 2

[node name="HapticLabel" type="Label" parent="SettingsPanel/VBox/HapticContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Haptic Feedback:"
vertical_alignment = 1

[node name="HapticCheckBox" type="CheckBox" parent="SettingsPanel/VBox/HapticContainer"]
layout_mode = 2
size_flags_horizontal = 3
button_pressed = true

[node name="SettingsBackButton" type="Button" parent="SettingsPanel/VBox"]
layout_mode = 2
text = "BACK"
