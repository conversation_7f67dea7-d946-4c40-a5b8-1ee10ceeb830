extends Control
class_name TouchControls

# Mobile touch controls for the platformer
signal move_left_pressed
signal move_left_released
signal move_right_pressed
signal move_right_released
signal jump_pressed
signal attack_pressed

# Control references
@onready var left_button: TouchScreenButton = $LeftButton
@onready var right_button: TouchScreenButton = $RightButton
@onready var jump_button: TouchScreenButton = $JumpButton
@onready var attack_button: TouchScreenButton = $AttackButton

# Virtual joystick (alternative to buttons)
@onready var joystick_base: Control = $VirtualJoystick/Base
@onready var joystick_knob: Control = $VirtualJoystick/Knob

# Touch state
var is_using_joystick: bool = false
var joystick_center: Vector2
var joystick_radius: float = 50.0
var current_touch_index: int = -1

# Input state
var move_direction: float = 0.0
var is_moving_left: bool = false
var is_moving_right: bool = false

func _ready():
	# Only show touch controls on mobile devices
	if not UIManager.is_touch_device():
		visible = false
		return
	
	setup_touch_controls()
	connect_signals()

func setup_touch_controls():
	# Set up button positions and sizes based on screen size
	var screen_size = get_viewport().size
	var ui_scale = UIManager.get_ui_scale()
	
	# Scale buttons appropriately
	var button_size = Vector2(80, 80) * ui_scale
	
	# Position left/right movement buttons
	left_button.position = Vector2(50 * ui_scale, screen_size.y - 150 * ui_scale)
	left_button.size = button_size
	
	right_button.position = Vector2(150 * ui_scale, screen_size.y - 150 * ui_scale)
	right_button.size = button_size
	
	# Position action buttons
	jump_button.position = Vector2(screen_size.x - 150 * ui_scale, screen_size.y - 150 * ui_scale)
	jump_button.size = button_size
	
	attack_button.position = Vector2(screen_size.x - 250 * ui_scale, screen_size.y - 150 * ui_scale)
	attack_button.size = button_size
	
	# Set up virtual joystick
	joystick_base.position = Vector2(100 * ui_scale, screen_size.y - 150 * ui_scale)
	joystick_base.size = Vector2(100, 100) * ui_scale
	joystick_center = joystick_base.position + joystick_base.size / 2
	joystick_radius = 40 * ui_scale
	
	# Initially hide joystick (can be toggled in settings)
	$VirtualJoystick.visible = false

func connect_signals():
	# Connect button signals
	left_button.pressed.connect(_on_left_pressed)
	left_button.released.connect(_on_left_released)
	right_button.pressed.connect(_on_right_pressed)
	right_button.released.connect(_on_right_released)
	jump_button.pressed.connect(_on_jump_pressed)
	attack_button.pressed.connect(_on_attack_pressed)

func _input(event):
	if not visible:
		return
	
	# Handle virtual joystick input
	if is_using_joystick:
		handle_joystick_input(event)

func handle_joystick_input(event):
	if event is InputEventScreenTouch:
		if event.pressed:
			var touch_pos = event.position
			var distance = touch_pos.distance_to(joystick_center)
			
			if distance <= joystick_radius * 1.5:  # Allow some tolerance
				current_touch_index = event.index
				update_joystick_position(touch_pos)
		else:
			if event.index == current_touch_index:
				current_touch_index = -1
				reset_joystick()
	
	elif event is InputEventScreenDrag:
		if event.index == current_touch_index:
			update_joystick_position(event.position)

func update_joystick_position(touch_pos: Vector2):
	var offset = touch_pos - joystick_center
	var distance = offset.length()
	
	if distance > joystick_radius:
		offset = offset.normalized() * joystick_radius
	
	joystick_knob.position = joystick_base.position + joystick_base.size / 2 + offset - joystick_knob.size / 2
	
	# Calculate movement direction
	var new_direction = offset.x / joystick_radius
	update_movement_direction(new_direction)

func reset_joystick():
	joystick_knob.position = joystick_base.position + joystick_base.size / 2 - joystick_knob.size / 2
	update_movement_direction(0.0)

func update_movement_direction(direction: float):
	var threshold = 0.3
	
	# Handle left movement
	if direction < -threshold and not is_moving_left:
		is_moving_left = true
		is_moving_right = false
		move_left_pressed.emit()
		if is_moving_right:
			move_right_released.emit()
	elif direction >= -threshold and is_moving_left:
		is_moving_left = false
		move_left_released.emit()
	
	# Handle right movement
	if direction > threshold and not is_moving_right:
		is_moving_right = true
		is_moving_left = false
		move_right_pressed.emit()
		if is_moving_left:
			move_left_released.emit()
	elif direction <= threshold and is_moving_right:
		is_moving_right = false
		move_right_released.emit()
	
	move_direction = direction

func _on_left_pressed():
	move_left_pressed.emit()
	is_moving_left = true
	
	# Haptic feedback
	if UIManager:
		UIManager.trigger_haptic_feedback(0.2)

func _on_left_released():
	move_left_released.emit()
	is_moving_left = false

func _on_right_pressed():
	move_right_pressed.emit()
	is_moving_right = true
	
	# Haptic feedback
	if UIManager:
		UIManager.trigger_haptic_feedback(0.2)

func _on_right_released():
	move_right_released.emit()
	is_moving_right = false

func _on_jump_pressed():
	jump_pressed.emit()
	
	# Haptic feedback
	if UIManager:
		UIManager.trigger_haptic_feedback(0.4)
	
	# Visual feedback
	animate_button_press(jump_button)

func _on_attack_pressed():
	attack_pressed.emit()
	
	# Haptic feedback
	if UIManager:
		UIManager.trigger_haptic_feedback(0.3)
	
	# Visual feedback
	animate_button_press(attack_button)

func animate_button_press(button: TouchScreenButton):
	var tween = create_tween()
	tween.tween_property(button, "scale", Vector2(0.9, 0.9), 0.1)
	tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.1)

func set_joystick_mode(enabled: bool):
	is_using_joystick = enabled
	$VirtualJoystick.visible = enabled
	left_button.visible = not enabled
	right_button.visible = not enabled

func set_controls_opacity(opacity: float):
	modulate.a = clamp(opacity, 0.3, 1.0)

func set_controls_scale(scale: float):
	var ui_scale = clamp(scale, 0.5, 2.0)
	
	# Update button sizes
	var button_size = Vector2(80, 80) * ui_scale
	left_button.size = button_size
	right_button.size = button_size
	jump_button.size = button_size
	attack_button.size = button_size
	
	# Update joystick size
	joystick_base.size = Vector2(100, 100) * ui_scale
	joystick_radius = 40 * ui_scale

func hide_controls():
	visible = false

func show_controls():
	if UIManager.is_touch_device():
		visible = true

# Custom input handling for integration with Godot's input system
func _unhandled_input(_event):
	if not visible:
		return
	
	# Convert touch controls to input actions
	if is_moving_left and not Input.is_action_pressed("move_left"):
		Input.action_press("move_left")
	elif not is_moving_left and Input.is_action_pressed("move_left"):
		Input.action_release("move_left")
	
	if is_moving_right and not Input.is_action_pressed("move_right"):
		Input.action_press("move_right")
	elif not is_moving_right and Input.is_action_pressed("move_right"):
		Input.action_release("move_right")
