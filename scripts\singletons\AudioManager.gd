extends Node

# Audio management singleton
signal music_volume_changed(volume: float)
signal sfx_volume_changed(volume: float)

# Audio players
var music_player: AudioStreamPlayer
var sfx_players: Array[AudioStreamPlayer] = []
var sfx_pool_size: int = 10

# Volume settings
var master_volume: float = 1.0
var music_volume: float = 0.7
var sfx_volume: float = 0.8
var is_muted: bool = false

# Audio resources (will be loaded when available)
var audio_library: Dictionary = {}

# Current music track
var current_music: String = ""
var music_fade_duration: float = 1.0

func _ready():
	process_mode = Node.PROCESS_MODE_ALWAYS
	setup_audio_players()
	load_audio_settings()

func setup_audio_players():
	# Create music player
	music_player = AudioStreamPlayer.new()
	music_player.name = "MusicPlayer"
	music_player.bus = "Music"
	add_child(music_player)
	
	# Create SFX player pool
	for i in sfx_pool_size:
		var sfx_player = AudioStreamPlayer.new()
		sfx_player.name = "SFXPlayer" + str(i)
		sfx_player.bus = "SFX"
		add_child(sfx_player)
		sfx_players.append(sfx_player)

func load_audio_settings():
	# Load from save data when available
	if SaveManager:
		var data = SaveManager.get_audio_settings()
		if data:
			master_volume = data.get("master_volume", 1.0)
			music_volume = data.get("music_volume", 0.7)
			sfx_volume = data.get("sfx_volume", 0.8)
			is_muted = data.get("is_muted", false)
			apply_volume_settings()

func apply_volume_settings():
	var final_music_volume = master_volume * music_volume if not is_muted else 0.0
	var final_sfx_volume = master_volume * sfx_volume if not is_muted else 0.0
	
	# Apply to audio buses
	var music_bus_index = AudioServer.get_bus_index("Music")
	var sfx_bus_index = AudioServer.get_bus_index("SFX")
	
	if music_bus_index != -1:
		AudioServer.set_bus_volume_db(music_bus_index, linear_to_db(final_music_volume))
	if sfx_bus_index != -1:
		AudioServer.set_bus_volume_db(sfx_bus_index, linear_to_db(final_sfx_volume))

func play_music(track_name: String, fade_in: bool = true):
	if current_music == track_name and music_player.playing:
		return
	
	var audio_path = "res://assets/audio/music/" + track_name + ".ogg"
	if ResourceLoader.exists(audio_path):
		var audio_stream = load(audio_path)
		
		if fade_in and music_player.playing:
			fade_out_music()
			await get_tree().create_timer(music_fade_duration).timeout
		
		music_player.stream = audio_stream
		music_player.play()
		current_music = track_name
		
		if fade_in:
			fade_in_music()

func stop_music(fade_out: bool = true):
	if fade_out:
		fade_out_music()
		await get_tree().create_timer(music_fade_duration).timeout
	
	music_player.stop()
	current_music = ""

func fade_in_music():
	var tween = create_tween()
	music_player.volume_db = -80
	tween.tween_property(music_player, "volume_db", 0, music_fade_duration)

func fade_out_music():
	var tween = create_tween()
	tween.tween_property(music_player, "volume_db", -80, music_fade_duration)

func play_sfx(sfx_name: String, pitch_variation: float = 0.0):
	var audio_path = "res://assets/audio/sfx/" + sfx_name + ".ogg"
	if ResourceLoader.exists(audio_path):
		var audio_stream = load(audio_path)
		var player = get_available_sfx_player()
		
		if player:
			player.stream = audio_stream
			player.pitch_scale = 1.0 + randf_range(-pitch_variation, pitch_variation)
			player.play()

func play_sfx_positional(sfx_name: String, position: Vector2, pitch_variation: float = 0.0):
	# For 2D positional audio, we'll use AudioStreamPlayer2D
	var audio_path = "res://assets/audio/sfx/" + sfx_name + ".ogg"
	if ResourceLoader.exists(audio_path):
		var audio_stream = load(audio_path)
		var player = AudioStreamPlayer2D.new()
		get_tree().current_scene.add_child(player)
		
		player.stream = audio_stream
		player.global_position = position
		player.pitch_scale = 1.0 + randf_range(-pitch_variation, pitch_variation)
		player.play()
		
		# Remove player after sound finishes
		player.finished.connect(func(): player.queue_free())

func get_available_sfx_player() -> AudioStreamPlayer:
	for player in sfx_players:
		if not player.playing:
			return player
	
	# If all players are busy, use the first one
	return sfx_players[0]

func set_master_volume(volume: float):
	master_volume = clamp(volume, 0.0, 1.0)
	apply_volume_settings()
	save_audio_settings()

func set_music_volume(volume: float):
	music_volume = clamp(volume, 0.0, 1.0)
	apply_volume_settings()
	save_audio_settings()
	music_volume_changed.emit(music_volume)

func set_sfx_volume(volume: float):
	sfx_volume = clamp(volume, 0.0, 1.0)
	apply_volume_settings()
	save_audio_settings()
	sfx_volume_changed.emit(sfx_volume)

func toggle_mute():
	is_muted = not is_muted
	apply_volume_settings()
	save_audio_settings()

func save_audio_settings():
	if SaveManager:
		var audio_data = {
			"master_volume": master_volume,
			"music_volume": music_volume,
			"sfx_volume": sfx_volume,
			"is_muted": is_muted
		}
		SaveManager.save_audio_settings(audio_data)

# Predefined sound effects for the game
func play_jump_sound():
	play_sfx("jump", 0.1)

func play_land_sound():
	play_sfx("land", 0.1)

func play_coin_collect_sound():
	play_sfx("coin_collect", 0.2)

func play_enemy_hit_sound():
	play_sfx("enemy_hit", 0.1)

func play_player_hurt_sound():
	play_sfx("player_hurt", 0.1)

func play_power_up_sound():
	play_sfx("power_up", 0.1)

func play_checkpoint_sound():
	play_sfx("checkpoint", 0.0)

func play_level_complete_sound():
	play_sfx("level_complete", 0.0)

func play_game_over_sound():
	play_sfx("game_over", 0.0)

func play_button_click_sound():
	play_sfx("button_click", 0.05)

func play_menu_music():
	play_music("menu_theme")

func play_level_music():
	play_music("level_theme")

func play_boss_music():
	play_music("boss_theme")
