extends Node

# Game state management singleton
signal game_started
signal game_paused
signal game_resumed
signal game_over
signal level_completed
signal player_died
signal score_changed(new_score: int)
signal coins_changed(new_coins: int)
signal lives_changed(new_lives: int)
signal health_changed(new_health: int)

enum GameState {
	MENU,
	PLAYING,
	PAUSED,
	GAME_OVER,
	LEVEL_COMPLETE
}

# Game state
var current_state: GameState = GameState.MENU
var current_level: int = 1
var max_unlocked_level: int = 1

# Player stats
var score: int = 0
var coins: int = 0
var lives: int = 3
var max_health: int = 3
var current_health: int = 3

# Level progress
var checkpoints_reached: Array[Vector2] = []
var current_checkpoint: Vector2 = Vector2.ZERO

# Performance settings
var target_fps: int = 60
var mobile_optimizations: bool = true

# Monetization data
var cosmetic_unlocks: Dictionary = {}
var achievements: Dictionary = {}
var premium_content_unlocked: bool = false

func _ready():
	# Set up the game
	process_mode = Node.PROCESS_MODE_ALWAYS
	Engine.max_fps = target_fps
	
	# Load saved data
	load_game_data()
	
	# Connect to save manager
	if SaveManager:
		SaveManager.data_loaded.connect(_on_data_loaded)

func _on_data_loaded(data: Dictionary):
	if data.has("max_unlocked_level"):
		max_unlocked_level = data.max_unlocked_level
	if data.has("cosmetic_unlocks"):
		cosmetic_unlocks = data.cosmetic_unlocks
	if data.has("achievements"):
		achievements = data.achievements
	if data.has("premium_content"):
		premium_content_unlocked = data.premium_content

func start_game(level: int = 1):
	current_level = level
	current_state = GameState.PLAYING
	reset_level_stats()
	game_started.emit()

func pause_game():
	if current_state == GameState.PLAYING:
		current_state = GameState.PAUSED
		get_tree().paused = true
		game_paused.emit()

func resume_game():
	if current_state == GameState.PAUSED:
		current_state = GameState.PLAYING
		get_tree().paused = false
		game_resumed.emit()

func end_game():
	current_state = GameState.GAME_OVER
	get_tree().paused = false

	# Show game over screen
	if UIManager:
		UIManager.show_game_over_screen()

	game_over.emit()

func complete_level():
	current_state = GameState.LEVEL_COMPLETE
	
	# Unlock next level
	if current_level >= max_unlocked_level:
		max_unlocked_level = current_level + 1
		save_game_data()
	
	level_completed.emit()

func player_death():
	lives -= 1
	lives_changed.emit(lives)
	player_died.emit()
	
	if lives <= 0:
		end_game()
	else:
		# Respawn at checkpoint
		respawn_player()

func respawn_player():
	current_health = max_health
	health_changed.emit(current_health)

func reset_level_stats():
	current_health = max_health
	checkpoints_reached.clear()
	current_checkpoint = Vector2.ZERO

func add_score(points: int):
	score += points
	score_changed.emit(score)

func add_coins(amount: int):
	coins += amount
	coins_changed.emit(coins)

func spend_coins(amount: int) -> bool:
	if coins >= amount:
		coins -= amount
		coins_changed.emit(coins)
		return true
	return false

func take_damage(damage: int = 1):
	current_health = max(0, current_health - damage)
	health_changed.emit(current_health)
	
	if current_health <= 0:
		player_death()

func heal(amount: int = 1):
	current_health = min(max_health, current_health + amount)
	health_changed.emit(current_health)

func set_checkpoint(position: Vector2):
	current_checkpoint = position
	if position not in checkpoints_reached:
		checkpoints_reached.append(position)

func unlock_cosmetic(item_id: String):
	cosmetic_unlocks[item_id] = true
	save_game_data()

func unlock_achievement(achievement_id: String):
	if not achievements.has(achievement_id):
		achievements[achievement_id] = true
		save_game_data()
		# Show achievement notification
		if UIManager:
			UIManager.show_achievement(achievement_id)

func is_level_unlocked(level: int) -> bool:
	return level <= max_unlocked_level

func save_game_data():
	if SaveManager:
		var data = {
			"max_unlocked_level": max_unlocked_level,
			"cosmetic_unlocks": cosmetic_unlocks,
			"achievements": achievements,
			"premium_content": premium_content_unlocked,
			"coins": coins,
			"high_score": score
		}
		SaveManager.save_data(data)

func load_game_data():
	if SaveManager:
		SaveManager.load_data()

func get_achievement_progress(achievement_id: String) -> bool:
	return achievements.get(achievement_id, false)

func reset_game():
	score = 0
	coins = 0
	lives = 3
	current_health = max_health
	current_level = 1
	current_state = GameState.MENU
	reset_level_stats()
	
	# Emit all change signals
	score_changed.emit(score)
	coins_changed.emit(coins)
	lives_changed.emit(lives)
	health_changed.emit(current_health)
