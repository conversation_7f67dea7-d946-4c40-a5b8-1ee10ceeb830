[gd_scene load_steps=12 format=3 uid="uid://dvj0262n6qu60"]

[ext_resource type="Script" uid="uid://b4u4t1fyfvoac" path="res://scripts/levels/Level.gd" id="1_level"]
[ext_resource type="PackedScene" uid="uid://bqxvn8ywqxqxr" path="res://scenes/player/Player.tscn" id="2_player"]
[ext_resource type="PackedScene" uid="uid://bvj0262n6qu60" path="res://scenes/ui/GameHUD.tscn" id="3_hud"]
[ext_resource type="PackedScene" uid="uid://dqxvn8ywqxqxr" path="res://scenes/collectibles/Coin.tscn" id="4_coin"]
[ext_resource type="PackedScene" uid="uid://vj0262n6qu60" path="res://scenes/enemies/Slime.tscn" id="5_slime"]
[ext_resource type="Texture2D" uid="uid://cf24ltpjgtbye" path="res://assets/sprites/environment/background.png" id="6_bg"]
[ext_resource type="PackedScene" uid="uid://c045h45fmj0f8" path="res://scenes/ui/PauseMenu.tscn" id="7_pause"]
[ext_resource type="PackedScene" uid="uid://045h45fmj0f8" path="res://scenes/collectibles/Checkpoint.tscn" id="8_checkpoint"]
[ext_resource type="PackedScene" uid="uid://d045h45fmj0f8" path="res://scenes/ui/GameOverScreen.tscn" id="9_gameover"]
[ext_resource type="PackedScene" uid="uid://5qav2752c4oh" path="res://scenes/ui/LevelCompleteScreen.tscn" id="10_complete"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_exit"]
size = Vector2(64, 128)

[node name="TestLevel" type="Node2D"]
script = ExtResource("1_level")
level_name = "Test Level"

[node name="Background" type="TextureRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = 2000.0
offset_bottom = 1200.0
texture = ExtResource("6_bg")
stretch_mode = 1

[node name="Player" parent="." instance=ExtResource("2_player")]
position = Vector2(100, 400)

[node name="UI" type="CanvasLayer" parent="."]

[node name="GameHUD" parent="UI" instance=ExtResource("3_hud")]
grow_horizontal = 2
grow_vertical = 2

[node name="PauseMenu" parent="UI" instance=ExtResource("7_pause")]
grow_horizontal = 2
grow_vertical = 2

[node name="GameOverScreen" parent="UI" instance=ExtResource("9_gameover")]
grow_horizontal = 2
grow_vertical = 2

[node name="LevelCompleteScreen" parent="UI" instance=ExtResource("10_complete")]
grow_horizontal = 2
grow_vertical = 2

[node name="Platforms" type="StaticBody2D" parent="."]
collision_layer = 4
collision_mask = 0

[node name="Ground" type="CollisionShape2D" parent="Platforms"]
position = Vector2(1000, 600)
shape = SubResource("RectangleShape2D_exit")

[node name="Platform1" type="CollisionShape2D" parent="Platforms"]
position = Vector2(300, 500)
shape = SubResource("RectangleShape2D_exit")

[node name="Platform2" type="CollisionShape2D" parent="Platforms"]
position = Vector2(600, 400)
shape = SubResource("RectangleShape2D_exit")

[node name="Platform3" type="CollisionShape2D" parent="Platforms"]
position = Vector2(900, 300)
shape = SubResource("RectangleShape2D_exit")

[node name="Collectibles" type="Node2D" parent="."]

[node name="Coin1" parent="Collectibles" instance=ExtResource("4_coin")]
position = Vector2(200, 450)

[node name="Coin2" parent="Collectibles" instance=ExtResource("4_coin")]
position = Vector2(400, 350)

[node name="Coin3" parent="Collectibles" instance=ExtResource("4_coin")]
position = Vector2(700, 250)

[node name="Checkpoint1" parent="Collectibles" instance=ExtResource("8_checkpoint")]
position = Vector2(600, 350)

[node name="Enemies" type="Node2D" parent="."]

[node name="Slime1" parent="Enemies" instance=ExtResource("5_slime")]
position = Vector2(500, 450)

[node name="LevelExit" type="Area2D" parent="."]
position = Vector2(1200, 400)
collision_layer = 0

[node name="CollisionShape2D" type="CollisionShape2D" parent="LevelExit"]
shape = SubResource("RectangleShape2D_exit")

[node name="ExitSign" type="Label" parent="LevelExit"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -32.0
offset_top = -12.0
offset_right = 32.0
offset_bottom = 12.0
text = "EXIT"
horizontal_alignment = 1
vertical_alignment = 1
