[gd_scene load_steps=8 format=3 uid="uid://hqxvn8ywqxqxr"]

[ext_resource type="Script" path="res://scripts/levels/Level.gd" id="1_level"]
[ext_resource type="PackedScene" uid="uid://bqxvn8ywqxqxr" path="res://scenes/player/Player.tscn" id="2_player"]
[ext_resource type="PackedScene" uid="uid://fqxvn8ywqxqxr" path="res://scenes/ui/GameHUD.tscn" id="3_hud"]
[ext_resource type="PackedScene" uid="uid://dqxvn8ywqxqxr" path="res://scenes/collectibles/Coin.tscn" id="4_coin"]
[ext_resource type="PackedScene" uid="uid://eqxvn8ywqxqxr" path="res://scenes/enemies/Slime.tscn" id="5_slime"]
[ext_resource type="Texture2D" uid="uid://bfqhqxqxqxqxr" path="res://assets/sprites/environment/background.png" id="6_bg"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_exit"]
size = Vector2(64, 128)

[node name="TestLevel" type="Node2D"]
script = ExtResource("1_level")
level_name = "Test Level"
level_number = 1

[node name="Background" type="TextureRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = 2000.0
offset_bottom = 1200.0
texture = ExtResource("6_bg")
stretch_mode = 1

[node name="Player" parent="." instance=ExtResource("2_player")]
position = Vector2(100, 400)

[node name="UI" type="CanvasLayer" parent="."]

[node name="GameHUD" parent="UI" instance=ExtResource("3_hud")]

[node name="Platforms" type="StaticBody2D" parent="."]
collision_layer = 4
collision_mask = 0

[node name="Ground" type="CollisionShape2D" parent="Platforms"]
position = Vector2(1000, 600)
shape = SubResource("RectangleShape2D_exit")

[node name="Platform1" type="CollisionShape2D" parent="Platforms"]
position = Vector2(300, 500)
shape = SubResource("RectangleShape2D_exit")

[node name="Platform2" type="CollisionShape2D" parent="Platforms"]
position = Vector2(600, 400)
shape = SubResource("RectangleShape2D_exit")

[node name="Platform3" type="CollisionShape2D" parent="Platforms"]
position = Vector2(900, 300)
shape = SubResource("RectangleShape2D_exit")

[node name="Collectibles" type="Node2D" parent="."]

[node name="Coin1" parent="Collectibles" instance=ExtResource("4_coin")]
position = Vector2(200, 450)

[node name="Coin2" parent="Collectibles" instance=ExtResource("4_coin")]
position = Vector2(400, 350)

[node name="Coin3" parent="Collectibles" instance=ExtResource("4_coin")]
position = Vector2(700, 250)

[node name="Enemies" type="Node2D" parent="."]

[node name="Slime1" parent="Enemies" instance=ExtResource("5_slime")]
position = Vector2(500, 450)

[node name="LevelExit" type="Area2D" parent="."]
position = Vector2(1200, 400)
collision_layer = 0
collision_mask = 1

[node name="CollisionShape2D" type="CollisionShape2D" parent="LevelExit"]
shape = SubResource("RectangleShape2D_exit")

[node name="ExitSign" type="Label" parent="LevelExit"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -32.0
offset_top = -12.0
offset_right = 32.0
offset_bottom = 12.0
text = "EXIT"
horizontal_alignment = 1
vertical_alignment = 1