extends Control
class_name GameHUD

# Main game HUD with health, coins, score, and mobile controls
@onready var health_container: HBoxContainer = $TopBar/LeftSide/HealthContainer
@onready var coins_label: Label = $TopBar/LeftSide/CoinsContainer/CoinsLabel
@onready var score_label: Label = $TopBar/RightSide/ScoreContainer/ScoreLabel
@onready var lives_label: Label = $TopBar/RightSide/LivesContainer/LivesLabel
@onready var pause_button: Button = $TopBar/RightSide/PauseButton
@onready var touch_controls: TouchControls = $TouchControls

# Health heart textures
var heart_full_texture: Texture2D
var heart_empty_texture: Texture2D

# Current values
var current_health: int = 3
var max_health: int = 3
var current_coins: int = 0
var current_score: int = 0
var current_lives: int = 3

func _ready():
	# Load heart textures
	heart_full_texture = load("res://assets/sprites/ui/hearts_hud.png")
	heart_empty_texture = load("res://assets/sprites/ui/no_hearts_hud.png")
	
	# Connect to GameManager signals
	if GameManager:
		GameManager.health_changed.connect(_on_health_changed)
		GameManager.coins_changed.connect(_on_coins_changed)
		GameManager.score_changed.connect(_on_score_changed)
		GameManager.lives_changed.connect(_on_lives_changed)
		GameManager.game_paused.connect(_on_game_paused)
		GameManager.game_resumed.connect(_on_game_resumed)
	
	# Connect pause button
	pause_button.pressed.connect(_on_pause_pressed)
	
	# Register with UIManager
	if UIManager:
		UIManager.register_hud(self)
	
	# Initialize display
	update_health_display()
	update_coins_display()
	update_score_display()
	update_lives_display()
	
	# Set up touch controls
	setup_touch_controls()

func setup_touch_controls():
	if touch_controls and UIManager.is_touch_device():
		# Connect touch control signals to input actions
		touch_controls.move_left_pressed.connect(_on_move_left_pressed)
		touch_controls.move_left_released.connect(_on_move_left_released)
		touch_controls.move_right_pressed.connect(_on_move_right_pressed)
		touch_controls.move_right_released.connect(_on_move_right_released)
		touch_controls.jump_pressed.connect(_on_jump_pressed)
		touch_controls.attack_pressed.connect(_on_attack_pressed)

func _on_health_changed(new_health: int):
	current_health = new_health
	update_health_display()

func _on_coins_changed(new_coins: int):
	current_coins = new_coins
	update_coins_display()

func _on_score_changed(new_score: int):
	current_score = new_score
	update_score_display()

func _on_lives_changed(new_lives: int):
	current_lives = new_lives
	update_lives_display()

func update_health_display():
	# Clear existing hearts
	for child in health_container.get_children():
		child.queue_free()
	
	# Add heart icons
	for i in max_health:
		var heart_icon = TextureRect.new()
		heart_icon.custom_minimum_size = Vector2(32, 32)
		heart_icon.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
		
		if i < current_health:
			heart_icon.texture = heart_full_texture
		else:
			heart_icon.texture = heart_empty_texture
		
		health_container.add_child(heart_icon)

func update_coins_display():
	coins_label.text = str(current_coins)
	
	# Animate coin count change
	var tween = create_tween()
	tween.tween_property(coins_label, "scale", Vector2(1.2, 1.2), 0.1)
	tween.tween_property(coins_label, "scale", Vector2(1.0, 1.0), 0.1)

func update_score_display():
	score_label.text = str(current_score)

func update_lives_display():
	lives_label.text = "x" + str(current_lives)

func _on_pause_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	if GameManager:
		GameManager.pause_game()

func _on_game_paused():
	# Show pause menu
	if UIManager:
		UIManager.show_pause_menu()

func _on_game_resumed():
	# Hide pause menu
	if UIManager:
		UIManager.hide_pause_menu()

# Touch control input handlers
func _on_move_left_pressed():
	Input.action_press("move_left")

func _on_move_left_released():
	Input.action_release("move_left")

func _on_move_right_pressed():
	Input.action_press("move_right")

func _on_move_right_released():
	Input.action_release("move_right")

func _on_jump_pressed():
	Input.action_press("jump")
	# Release after a short delay to simulate button press
	await get_tree().create_timer(0.1).timeout
	Input.action_release("jump")

func _on_attack_pressed():
	Input.action_press("attack")
	# Release after a short delay to simulate button press
	await get_tree().create_timer(0.1).timeout
	Input.action_release("attack")

func show_level_info(level_name: String, level_number: int):
	# Create temporary level info display
	var level_info = Label.new()
	level_info.text = "Level " + str(level_number) + "\n" + level_name
	level_info.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	level_info.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	level_info.add_theme_font_size_override("font_size", 24)
	level_info.modulate = Color.TRANSPARENT
	
	# Position in center of screen
	level_info.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	add_child(level_info)
	
	# Animate in and out
	var tween = create_tween()
	tween.tween_property(level_info, "modulate", Color.WHITE, 0.5)
	tween.tween_delay(2.0)
	tween.tween_property(level_info, "modulate", Color.TRANSPARENT, 0.5)
	
	await tween.finished
	level_info.queue_free()

func show_damage_indicator():
	# Flash red overlay for damage
	var damage_overlay = ColorRect.new()
	damage_overlay.color = Color(1, 0, 0, 0.3)
	damage_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(damage_overlay)
	
	var tween = create_tween()
	tween.tween_property(damage_overlay, "modulate:a", 0.0, 0.3)
	
	await tween.finished
	damage_overlay.queue_free()

func show_coin_collected_effect(_position: Vector2):
	# Show floating "+1" text at coin position
	var coin_text = Label.new()
	coin_text.text = "+1"
	coin_text.add_theme_font_size_override("font_size", 18)
	coin_text.modulate = Color.YELLOW
	coin_text.position = _position
	add_child(coin_text)
	
	var tween = create_tween()
	tween.parallel().tween_property(coin_text, "position:y", _position.y - 50, 1.0)
	tween.parallel().tween_property(coin_text, "modulate:a", 0.0, 1.0)
	
	await tween.finished
	coin_text.queue_free()

func set_touch_controls_visible(show_controls: bool):
	if touch_controls:
		touch_controls.visible = show_controls and UIManager.is_touch_device()

func update_max_health(new_max_health: int):
	max_health = new_max_health
	update_health_display()
