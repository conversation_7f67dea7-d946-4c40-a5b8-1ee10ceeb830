# How to Play - Mobile Platformer

## Game Overview
This is a 2D mobile platformer featuring a heroic character navigating through challenging levels, collecting coins, defeating enemies, and reaching the exit.

## Controls

### Desktop Controls:
- **A** or **Left Arrow**: Move left
- **D** or **Right Arrow**: Move right  
- **W**, **Space**, or **Up Arrow**: Jump
- **X**: Attack
- **Escape**: Pause game

### Mobile Controls:
- **Left/Right Buttons**: Move character
- **Jump Button**: Jump (tap again in air for double jump)
- **Attack Button**: Sword attack
- **Pause Button**: Pause the game

## Gameplay Features

### Character Abilities:
- **Running**: Smooth left/right movement
- **Jumping**: Single jump with variable height
- **Double Jump**: Second jump while in air
- **Sword Attack**: Defeat enemies with your sword
- **Health System**: 3 hearts, lose one when hit by enemies

### Collectibles:
- **Coins**: Collect for points and progression
- **Health Potions**: Restore lost health
- **Checkpoints**: Save your progress through the level

### Enemies:
- **Slimes**: Basic enemies that patrol and chase the player
- **Various Types**: Different enemies with unique behaviors (more to be added)

### Level Progression:
- **Multiple Levels**: Progress through increasingly difficult stages
- **Score System**: Earn points for coins, defeating enemies, and completion bonuses
- **Time Tracking**: Complete levels quickly for bonus points
- **Achievement System**: Unlock achievements for various accomplishments

## Game Screens

### Main Menu:
- **Play**: Start the game from the first level
- **Level Select**: Choose from unlocked levels
- **Settings**: Adjust audio and controls
- **Quit**: Exit the game

### Pause Menu:
- **Resume**: Continue playing
- **Restart Level**: Start the current level over
- **Settings**: Adjust game settings
- **Main Menu**: Return to the main menu

### Game Over Screen:
- **Restart Level**: Try the level again
- **Main Menu**: Return to the main menu

### Level Complete Screen:
- **Next Level**: Progress to the next level
- **Replay Level**: Play the level again
- **Main Menu**: Return to the main menu

## Tips for Success

1. **Collect All Coins**: Gathering all coins in a level provides bonus points
2. **Use Checkpoints**: Activate checkpoints to save your progress
3. **Master Double Jump**: Essential for reaching higher platforms
4. **Time Your Attacks**: Defeat enemies efficiently to avoid taking damage
5. **Explore Thoroughly**: Look for hidden areas and secret collectibles
6. **Perfect Runs**: Complete levels without taking damage for bonus points

## Mobile Optimization Features

- **Touch Controls**: Optimized button layout for mobile screens
- **Haptic Feedback**: Feel the action with vibration feedback
- **Responsive UI**: Adapts to different screen sizes
- **Performance**: Optimized for 60 FPS on mobile devices
- **Battery Efficient**: Designed for extended mobile gaming sessions

## Monetization Features (Framework)

- **Cosmetic Upgrades**: Character skins and visual effects (to be implemented)
- **Level Packs**: Additional premium levels (framework ready)
- **Achievement System**: Unlock rewards through gameplay
- **Progression System**: Level unlocking encourages continued play

## Technical Notes

- Built with **Godot Engine 4.x**
- Optimized for **Android** and **iOS** export
- **Save System**: Progress is automatically saved
- **Audio System**: Framework ready for music and sound effects
- **Modular Design**: Easy to add new levels, enemies, and features

## Getting Started

1. **Launch the Game**: Run the project in Godot or export to your device
2. **Start Playing**: The game begins with a test level demonstrating all features
3. **Learn the Controls**: Practice movement, jumping, and combat
4. **Collect Everything**: Gather coins and reach the exit
5. **Progress**: Unlock new levels and achievements

## Development Status

✅ **Complete Core Features**:
- Player movement and combat
- Enemy AI and interactions
- Collectible systems
- Mobile touch controls
- Save/load functionality
- UI and menu systems
- Level progression

🔄 **Ready for Enhancement**:
- Audio implementation
- Additional levels
- More enemy types
- Power-up systems
- Social features

The game provides a solid foundation for a commercial mobile platformer with engaging gameplay and monetization potential.
