extends Area2D
class_name Checkpoint

# Checkpoint system for player respawn
signal activated(position: Vector2)

@onready var sprite: AnimatedSprite2D = $AnimatedSprite2D
@onready var collision_shape: CollisionShape2D = $CollisionShape2D
@onready var activation_particles: CPUParticles2D = $ActivationParticles

var is_activated: bool = false

func _ready():
	# Set up collision layers
	collision_layer = 64  # Triggers layer
	collision_mask = 0    # Don't collide with anything
	
	# Connect signals
	body_entered.connect(_on_body_entered)
	
	# Start with inactive animation
	if sprite:
		sprite.play("inactive")
	
	# Set up particles
	if activation_particles:
		activation_particles.emitting = false

func _on_body_entered(body):
	if body is Player and not is_activated:
		activate_checkpoint()

func activate_checkpoint():
	if is_activated:
		return
	
	is_activated = true
	
	# Change to active animation
	if sprite:
		sprite.play("active")
	
	# Emit particles
	if activation_particles:
		activation_particles.emitting = true
	
	# Play sound effect
	if AudioManager:
		AudioManager.play_checkpoint_sound()
	
	# Trigger haptic feedback
	if UIManager:
		UIManager.trigger_haptic_feedback(0.4)
	
	# Set as current checkpoint in GameManager
	if GameManager:
		GameManager.set_checkpoint(global_position)
	
	# Emit signal
	activated.emit(global_position)
	
	# Show activation effect
	animate_activation()

func animate_activation():
	# Scale pulse animation
	var tween = create_tween()
	tween.tween_property(self, "scale", Vector2(1.2, 1.2), 0.2)
	tween.tween_property(self, "scale", Vector2(1.0, 1.0), 0.2)

# For save/load functionality
func get_save_data() -> Dictionary:
	return {
		"position": global_position,
		"activated": is_activated
	}

func load_save_data(data: Dictionary):
	if data.has("position"):
		global_position = data.position
	
	if data.has("activated") and data.activated:
		is_activated = true
		if sprite:
			sprite.play("active")
