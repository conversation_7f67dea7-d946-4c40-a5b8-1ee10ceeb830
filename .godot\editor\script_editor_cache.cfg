[res://scripts/singletons/UIManager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 15.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/singletons/SaveManager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 9,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 193,
"scroll_position": 175.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/singletons/GameManager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 18.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/singletons/AudioManager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/ui/TouchControls.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 51,
"scroll_position": 39.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/levels/Level.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 86,
"scroll_position": 86.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
