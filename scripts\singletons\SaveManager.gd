extends Node

# Save/Load management singleton
signal data_loaded(data: Dictionary)
signal data_saved
signal save_error(error_message: String)

const SAVE_FILE_PATH = "user://save_game.dat"
const SETTINGS_FILE_PATH = "user://settings.dat"

var game_data: Dictionary = {}
var settings_data: Dictionary = {}

func _ready():
	process_mode = Node.PROCESS_MODE_ALWAYS

func save_data(data: Dictionary):
	game_data.merge(data, true)
	
	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.WRITE)
	if file:
		var json_string = JSON.stringify(game_data)
		file.store_string(json_string)
		file.close()
		data_saved.emit()
	else:
		save_error.emit("Failed to open save file for writing")

func load_data() -> Dictionary:
	if FileAccess.file_exists(SAVE_FILE_PATH):
		var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.READ)
		if file:
			var json_string = file.get_as_text()
			file.close()
			
			var json = JSON.new()
			var parse_result = json.parse(json_string)
			
			if parse_result == OK:
				game_data = json.data
				data_loaded.emit(game_data)
				return game_data
			else:
				save_error.emit("Failed to parse save file")
		else:
			save_error.emit("Failed to open save file for reading")
	
	# Return default data if no save file exists
	game_data = get_default_game_data()
	data_loaded.emit(game_data)
	return game_data

func get_default_game_data() -> Dictionary:
	return {
		"max_unlocked_level": 1,
		"cosmetic_unlocks": {},
		"achievements": {},
		"premium_content": false,
		"coins": 0,
		"high_score": 0,
		"total_playtime": 0.0,
		"levels_completed": 0,
		"enemies_defeated": 0,
		"coins_collected": 0,
		"deaths": 0
	}

func save_settings(settings: Dictionary):
	settings_data.merge(settings, true)
	
	var file = FileAccess.open(SETTINGS_FILE_PATH, FileAccess.WRITE)
	if file:
		var json_string = JSON.stringify(settings_data)
		file.store_string(json_string)
		file.close()
	else:
		save_error.emit("Failed to save settings")

func load_settings() -> Dictionary:
	if FileAccess.file_exists(SETTINGS_FILE_PATH):
		var file = FileAccess.open(SETTINGS_FILE_PATH, FileAccess.READ)
		if file:
			var json_string = file.get_as_text()
			file.close()
			
			var json = JSON.new()
			var parse_result = json.parse(json_string)
			
			if parse_result == OK:
				settings_data = json.data
				return settings_data
	
	# Return default settings
	settings_data = get_default_settings()
	return settings_data

func get_default_settings() -> Dictionary:
	return {
		"master_volume": 1.0,
		"music_volume": 0.7,
		"sfx_volume": 0.8,
		"is_muted": false,
		"haptic_feedback": true,
		"show_fps": false,
		"graphics_quality": "medium"
	}

func save_audio_settings(audio_data: Dictionary):
	var current_settings = load_settings()
	current_settings.merge(audio_data, true)
	save_settings(current_settings)

func get_audio_settings() -> Dictionary:
	var settings = load_settings()
	return {
		"master_volume": settings.get("master_volume", 1.0),
		"music_volume": settings.get("music_volume", 0.7),
		"sfx_volume": settings.get("sfx_volume", 0.8),
		"is_muted": settings.get("is_muted", false)
	}

func save_level_progress(level: int, score: int, coins: int, time: float):
	var level_key = "level_" + str(level)
	var level_data = game_data.get(level_key, {})
	
	# Update best scores
	var best_score = level_data.get("best_score", 0)
	var best_time = level_data.get("best_time", 999999.0)
	var total_coins = level_data.get("total_coins", 0)
	
	level_data["best_score"] = max(best_score, score)
	level_data["best_time"] = min(best_time, time)
	level_data["total_coins"] = total_coins + coins
	level_data["completed"] = true
	level_data["attempts"] = level_data.get("attempts", 0) + 1
	
	game_data[level_key] = level_data
	save_data({})

func get_level_progress(level: int) -> Dictionary:
	var level_key = "level_" + str(level)
	return game_data.get(level_key, {
		"best_score": 0,
		"best_time": 0.0,
		"total_coins": 0,
		"completed": false,
		"attempts": 0
	})

func update_statistics(stats: Dictionary):
	for key in stats:
		var current_value = game_data.get(key, 0)
		if stats[key] is int or stats[key] is float:
			game_data[key] = current_value + stats[key]
		else:
			game_data[key] = stats[key]
	
	save_data({})

func get_total_statistics() -> Dictionary:
	return {
		"total_playtime": game_data.get("total_playtime", 0.0),
		"levels_completed": game_data.get("levels_completed", 0),
		"enemies_defeated": game_data.get("enemies_defeated", 0),
		"coins_collected": game_data.get("coins_collected", 0),
		"deaths": game_data.get("deaths", 0),
		"high_score": game_data.get("high_score", 0)
	}

func delete_save_data():
	if FileAccess.file_exists(SAVE_FILE_PATH):
		DirAccess.remove_absolute(SAVE_FILE_PATH)
	game_data = get_default_game_data()
	data_loaded.emit(game_data)

func export_save_data() -> String:
	return JSON.stringify(game_data)

func import_save_data(json_string: String) -> bool:
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result == OK:
		game_data = json.data
		save_data({})
		return true
	
	return false

func has_save_data() -> bool:
	return FileAccess.file_exists(SAVE_FILE_PATH)

func get_save_file_size() -> int:
	if FileAccess.file_exists(SAVE_FILE_PATH):
		var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.READ)
		if file:
			var size = file.get_length()
			file.close()
			return size
	return 0
