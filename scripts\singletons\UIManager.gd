extends Node

# UI management singleton
signal achievement_shown(achievement_id: String)
signal notification_shown(message: String)

# UI references
var main_hud: Control
var pause_menu: Control
var game_over_screen: Control
var level_complete_screen: Control
var achievement_popup: Control
var notification_popup: Control

# Mobile UI settings
var touch_controls_enabled: bool = true
var haptic_feedback_enabled: bool = true
var ui_scale: float = 1.0

# Achievement definitions
var achievements: Dictionary = {
	"first_level": {
		"name": "Getting Started",
		"description": "Complete your first level",
		"icon": "res://assets/sprites/ui/achievement_first_level.png"
	},
	"coin_collector": {
		"name": "Coin Collector",
		"description": "Collect 100 coins",
		"icon": "res://assets/sprites/ui/achievement_coins.png"
	},
	"enemy_slayer": {
		"name": "Enemy Slayer",
		"description": "Defeat 50 enemies",
		"icon": "res://assets/sprites/ui/achievement_enemies.png"
	},
	"speed_runner": {
		"name": "Speed Runner",
		"description": "Complete a level in under 60 seconds",
		"icon": "res://assets/sprites/ui/achievement_speed.png"
	},
	"perfectionist": {
		"name": "Perfectionist",
		"description": "Complete a level without taking damage",
		"icon": "res://assets/sprites/ui/achievement_perfect.png"
	}
}

func _ready():
	process_mode = Node.PROCESS_MODE_ALWAYS
	load_ui_settings()
	setup_mobile_ui()

func load_ui_settings():
	if SaveManager:
		var settings = SaveManager.load_settings()
		haptic_feedback_enabled = settings.get("haptic_feedback", true)
		ui_scale = settings.get("ui_scale", 1.0)

func setup_mobile_ui():
	# Detect if running on mobile
	if OS.get_name() in ["Android", "iOS"]:
		touch_controls_enabled = true
		# Adjust UI scale based on screen size
		var screen_size = DisplayServer.screen_get_size()
		var base_width = 1080.0
		ui_scale = screen_size.x / base_width
		ui_scale = clamp(ui_scale, 0.8, 2.0)

func register_hud(hud: Control):
	main_hud = hud

func register_pause_menu(menu: Control):
	pause_menu = menu

func register_game_over_screen(screen: Control):
	game_over_screen = screen

func register_level_complete_screen(screen: Control):
	level_complete_screen = screen

func show_hud():
	if main_hud:
		main_hud.visible = true

func hide_hud():
	if main_hud:
		main_hud.visible = false

func show_pause_menu():
	if pause_menu:
		pause_menu.visible = true
		if AudioManager:
			AudioManager.play_button_click_sound()

func hide_pause_menu():
	if pause_menu:
		pause_menu.visible = false

func show_game_over_screen():
	if game_over_screen:
		if GameManager:
			var score = GameManager.score
			var coins = GameManager.coins_collected
			if game_over_screen.has_method("show_game_over"):
				game_over_screen.show_game_over(score, coins)
			else:
				game_over_screen.visible = true
		else:
			game_over_screen.visible = true
		hide_hud()

func hide_game_over_screen():
	if game_over_screen:
		game_over_screen.visible = false

func show_level_complete_screen():
	if level_complete_screen:
		if GameManager:
			var score = GameManager.score
			var coins = GameManager.coins_collected
			var level = GameManager.current_level
			var time = 60.0  # Placeholder time
			if level_complete_screen.has_method("show_level_complete"):
				level_complete_screen.show_level_complete(level, score, coins, time)
			else:
				level_complete_screen.visible = true
		else:
			level_complete_screen.visible = true
		hide_hud()

func hide_level_complete_screen():
	if level_complete_screen:
		level_complete_screen.visible = false

func show_achievement(achievement_id: String):
	if not achievements.has(achievement_id):
		return
	
	var achievement_data = achievements[achievement_id]
	
	# Create achievement popup if it doesn't exist
	if not achievement_popup:
		create_achievement_popup()
	
	# Update achievement popup content
	var title_label = achievement_popup.get_node("Panel/VBox/Title")
	var desc_label = achievement_popup.get_node("Panel/VBox/Description")
	var _achievement_icon = achievement_popup.get_node("Panel/VBox/HBox/Icon")

	title_label.text = achievement_data.name
	desc_label.text = achievement_data.description
	
	# Show the popup with animation
	achievement_popup.visible = true
	achievement_popup.modulate.a = 0.0
	achievement_popup.position.x = get_viewport().size.x
	
	var tween = create_tween()
	tween.parallel().tween_property(achievement_popup, "modulate:a", 1.0, 0.3)
	tween.parallel().tween_property(achievement_popup, "position:x", get_viewport().size.x - achievement_popup.size.x - 20, 0.3)
	
	# Hide after delay
	await get_tree().create_timer(3.0).timeout
	
	var hide_tween = create_tween()
	hide_tween.parallel().tween_property(achievement_popup, "modulate:a", 0.0, 0.3)
	hide_tween.parallel().tween_property(achievement_popup, "position:x", get_viewport().size.x, 0.3)
	
	await hide_tween.finished
	achievement_popup.visible = false
	
	achievement_shown.emit(achievement_id)

func create_achievement_popup():
	achievement_popup = Control.new()
	achievement_popup.name = "AchievementPopup"
	achievement_popup.set_anchors_and_offsets_preset(Control.PRESET_TOP_RIGHT)
	
	var panel = Panel.new()
	panel.name = "Panel"
	achievement_popup.add_child(panel)
	
	var vbox = VBoxContainer.new()
	vbox.name = "VBox"
	panel.add_child(vbox)
	
	var title = Label.new()
	title.name = "Title"
	title.add_theme_font_size_override("font_size", 18)
	vbox.add_child(title)
	
	var hbox = HBoxContainer.new()
	hbox.name = "HBox"
	vbox.add_child(hbox)
	
	var icon = TextureRect.new()
	icon.name = "Icon"
	icon.custom_minimum_size = Vector2(32, 32)
	hbox.add_child(icon)
	
	var desc = Label.new()
	desc.name = "Description"
	desc.add_theme_font_size_override("font_size", 14)
	desc.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	hbox.add_child(desc)
	
	get_tree().current_scene.add_child(achievement_popup)
	achievement_popup.visible = false

func show_notification(message: String, duration: float = 2.0):
	# Create simple notification
	if not notification_popup:
		create_notification_popup()
	
	var label = notification_popup.get_node("Panel/Label")
	label.text = message
	
	notification_popup.visible = true
	notification_popup.modulate.a = 0.0
	
	var tween = create_tween()
	tween.tween_property(notification_popup, "modulate:a", 1.0, 0.2)
	
	await get_tree().create_timer(duration).timeout
	
	var hide_tween = create_tween()
	hide_tween.tween_property(notification_popup, "modulate:a", 0.0, 0.2)
	await hide_tween.finished
	
	notification_popup.visible = false
	notification_shown.emit(message)

func create_notification_popup():
	notification_popup = Control.new()
	notification_popup.name = "NotificationPopup"
	notification_popup.set_anchors_and_offsets_preset(Control.PRESET_CENTER_TOP)
	
	var panel = Panel.new()
	panel.name = "Panel"
	notification_popup.add_child(panel)
	
	var label = Label.new()
	label.name = "Label"
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.add_theme_font_size_override("font_size", 16)
	panel.add_child(label)
	
	get_tree().current_scene.add_child(notification_popup)
	notification_popup.visible = false

func trigger_haptic_feedback(strength: float = 1.0):
	if haptic_feedback_enabled and OS.get_name() in ["Android", "iOS"]:
		# Trigger haptic feedback on mobile devices
		Input.vibrate_handheld(int(strength * 100))

func get_ui_scale() -> float:
	return ui_scale

func set_ui_scale(scale: float):
	ui_scale = clamp(scale, 0.5, 2.0)
	# Apply scale to UI elements
	apply_ui_scale()

func apply_ui_scale():
	# This would apply the scale to all registered UI elements
	pass

func is_touch_device() -> bool:
	return OS.get_name() in ["Android", "iOS"] or DisplayServer.is_touchscreen_available()
