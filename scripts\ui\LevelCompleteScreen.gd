extends Control
class_name LevelCompleteScreen

# Level complete screen with progression options
@onready var level_label: Label = $Panel/VBox/LevelLabel
@onready var score_label: Label = $Panel/VBox/ScoreLabel
@onready var coins_label: Label = $Panel/VBox/CoinsLabel
@onready var time_label: Label = $Panel/VBox/TimeLabel
@onready var next_level_button: Button = $Panel/VBox/ButtonContainer/NextLevelButton
@onready var replay_button: Button = $Panel/VBox/ButtonContainer/ReplayButton
@onready var main_menu_button: Button = $Panel/VBox/ButtonContainer/MainMenuButton
@onready var panel: Panel = $Panel

var current_level: int = 1
var final_score: int = 0
var final_coins: int = 0
var completion_time: float = 0.0

func _ready():
	# Initially hidden
	visible = false
	
	# Connect buttons
	next_level_button.pressed.connect(_on_next_level_pressed)
	replay_button.pressed.connect(_on_replay_pressed)
	main_menu_button.pressed.connect(_on_main_menu_pressed)
	
	# Register with UIManager
	if UIManager:
		UIManager.register_level_complete_screen(self)

func show_level_complete(level: int, score: int, coins: int, time: float):
	current_level = level
	final_score = score
	final_coins = coins
	completion_time = time
	
	# Update labels
	level_label.text = "Level " + str(level) + " Complete!"
	score_label.text = "Score: " + str(score)
	coins_label.text = "Coins: " + str(coins)
	time_label.text = "Time: " + str(int(time)) + "s"
	
	# Check if next level is available
	var next_level_available = GameManager and GameManager.is_level_unlocked(level + 1)
	next_level_button.disabled = not next_level_available
	
	if not next_level_available:
		next_level_button.text = "COMING SOON"
	else:
		next_level_button.text = "NEXT LEVEL"
	
	# Show screen
	visible = true
	
	# Animate in
	panel.scale = Vector2.ZERO
	panel.modulate.a = 0.0
	
	var tween = create_tween()
	tween.set_parallel(true)
	tween.tween_property(panel, "scale", Vector2.ONE, 0.5)
	tween.tween_property(panel, "modulate:a", 1.0, 0.5)

func _on_next_level_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	# Load next level
	var next_level_path = "res://scenes/levels/Level" + str(current_level + 1) + ".tscn"
	if ResourceLoader.exists(next_level_path):
		get_tree().change_scene_to_file(next_level_path)
	else:
		# No more levels, go to main menu
		get_tree().change_scene_to_file("res://scenes/ui/MainMenu.tscn")

func _on_replay_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	# Replay current level
	get_tree().reload_current_scene()

func _on_main_menu_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	# Return to main menu
	get_tree().change_scene_to_file("res://scenes/ui/MainMenu.tscn")

func hide_screen():
	visible = false
