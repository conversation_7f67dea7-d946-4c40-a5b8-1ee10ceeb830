extends CharacterBody2D
class_name Slime

# Basic slime enemy with patrol behavior
signal died
signal player_hit

@export var max_health: int = 2
@export var move_speed: float = 50.0
@export var patrol_distance: float = 100.0
@export var damage_to_player: int = 1
@export var points_value: int = 50

@onready var sprite: AnimatedSprite2D = $AnimatedSprite2D
@onready var collision_shape: CollisionShape2D = $CollisionShape2D
@onready var player_detector: Area2D = $PlayerDetector
@onready var wall_detector: RayCast2D = $WallDetector
@onready var floor_detector: RayCast2D = $FloorDetector
@onready var hit_particles: CPUParticles2D = $HitParticles
@onready var death_timer: Timer = $DeathTimer

# State variables
var current_health: int
var is_dead: bool = false
var is_hit: bool = false
var facing_direction: int = 1
var patrol_start_position: Vector2
var is_chasing_player: bool = false
var player_reference: Player = null

# AI state
enum EnemyState {
	PATROL,
	CHASE,
	HIT,
	DEATH
}

var current_state: EnemyState = EnemyState.PATROL

func _ready():
	# Initialize health
	current_health = max_health
	
	# Store starting position for patrol
	patrol_start_position = global_position
	
	# Set up collision layers
	collision_layer = 2  # Enemies layer
	collision_mask = 4 | 16  # Environment and Platforms
	
	# Set up player detector
	player_detector.collision_layer = 0
	player_detector.collision_mask = 1  # Player layer
	player_detector.body_entered.connect(_on_player_entered)
	player_detector.body_exited.connect(_on_player_exited)
	
	# Set up detectors
	wall_detector.collision_mask = 4 | 16  # Environment and Platforms
	floor_detector.collision_mask = 4 | 16
	
	# Connect timer
	death_timer.timeout.connect(_on_death_timer_timeout)
	death_timer.wait_time = 1.0
	
	# Start with idle animation
	sprite.play("idle")

func _physics_process(delta):
	if is_dead:
		return
	
	apply_gravity(delta)
	
	match current_state:
		EnemyState.PATROL:
			handle_patrol(delta)
		EnemyState.CHASE:
			handle_chase(delta)
		EnemyState.HIT:
			handle_hit(delta)
		EnemyState.DEATH:
			handle_death(delta)
	
	move_and_slide()
	update_sprite_direction()

func apply_gravity(delta):
	if not is_on_floor():
		velocity.y += get_gravity().y * delta

func handle_patrol(_delta):
	# Simple patrol behavior
	var distance_from_start = global_position.distance_to(patrol_start_position)

	# Check for walls or edges
	if wall_detector.is_colliding() or not floor_detector.is_colliding():
		facing_direction *= -1

	# Check patrol distance
	if distance_from_start > patrol_distance:
		# Turn around if too far from start
		var direction_to_start = (patrol_start_position - global_position).normalized()
		facing_direction = sign(direction_to_start.x)

	# Move
	velocity.x = facing_direction * move_speed

	# Play walk animation
	if abs(velocity.x) > 10:
		sprite.play("walk")
	else:
		sprite.play("idle")

func handle_chase(_delta):
	if not player_reference:
		current_state = EnemyState.PATROL
		return

	# Move towards player
	var direction_to_player = (player_reference.global_position - global_position).normalized()
	facing_direction = sign(direction_to_player.x)

	# Check for walls or edges
	if wall_detector.is_colliding() or not floor_detector.is_colliding():
		# Can't reach player, go back to patrol
		current_state = EnemyState.PATROL
		return

	velocity.x = facing_direction * move_speed * 1.5  # Move faster when chasing
	sprite.play("walk")

func handle_hit(_delta):
	# Brief hit state
	velocity.x = 0
	sprite.play("hit")

func handle_death(_delta):
	velocity.x = 0
	sprite.play("death")

func take_damage(damage: int = 1):
	if is_dead or is_hit:
		return
	
	current_health -= damage
	
	# Play hit effect
	if hit_particles:
		hit_particles.emitting = true
	
	if AudioManager:
		AudioManager.play_enemy_hit_sound()
	
	if current_health <= 0:
		die()
	else:
		# Enter hit state briefly
		current_state = EnemyState.HIT
		is_hit = true
		
		# Return to normal state after hit
		await get_tree().create_timer(0.3).timeout
		is_hit = false
		
		if is_chasing_player:
			current_state = EnemyState.CHASE
		else:
			current_state = EnemyState.PATROL

func die():
	if is_dead:
		return
	
	is_dead = true
	current_state = EnemyState.DEATH
	
	# Disable collision
	collision_shape.disabled = true
	player_detector.collision_shape_2d.disabled = true
	
	# Award points
	if GameManager:
		GameManager.add_score(points_value)
	
	# Play death sound
	if AudioManager:
		AudioManager.play_sfx("enemy_death", 0.1)
	
	# Emit signal
	died.emit()
	
	# Start death timer
	death_timer.start()

func _on_death_timer_timeout():
	queue_free()

func _on_player_entered(body):
	if body is Player and not is_dead:
		player_reference = body
		is_chasing_player = true
		current_state = EnemyState.CHASE

func _on_player_exited(body):
	if body is Player:
		player_reference = null
		is_chasing_player = false
		current_state = EnemyState.PATROL

func update_sprite_direction():
	sprite.flip_h = facing_direction < 0
	
	# Update detector directions
	wall_detector.target_position.x = abs(wall_detector.target_position.x) * facing_direction
	floor_detector.position.x = abs(floor_detector.position.x) * facing_direction

func _on_body_entered(body):
	# Handle collision with player (damage)
	if body is Player and not is_dead:
		if body.has_method("take_damage"):
			body.take_damage(damage_to_player)
			player_hit.emit()

# For save/load functionality
func get_save_data() -> Dictionary:
	return {
		"position": global_position,
		"health": current_health,
		"is_dead": is_dead,
		"facing_direction": facing_direction
	}

func load_save_data(data: Dictionary):
	if data.has("position"):
		global_position = data.position
	
	if data.has("health"):
		current_health = data.health
	
	if data.has("is_dead") and data.is_dead:
		die()
	
	if data.has("facing_direction"):
		facing_direction = data.facing_direction
