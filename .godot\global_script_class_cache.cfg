list=[{
"base": &"Area2D",
"class": &"Checkpoint",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/collectibles/Checkpoint.gd"
}, {
"base": &"Area2D",
"class": &"Coin",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/collectibles/Coin.gd"
}, {
"base": &"Control",
"class": &"GameHUD",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ui/GameHUD.gd"
}, {
"base": &"Control",
"class": &"GameOverScreen",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ui/GameOverScreen.gd"
}, {
"base": &"Area2D",
"class": &"HealthPotion",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/collectibles/HealthPotion.gd"
}, {
"base": &"Node2D",
"class": &"Level",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/levels/Level.gd"
}, {
"base": &"Control",
"class": &"LevelCompleteScreen",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ui/LevelCompleteScreen.gd"
}, {
"base": &"Control",
"class": &"MainMenu",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ui/MainMenu.gd"
}, {
"base": &"Control",
"class": &"PauseMenu",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ui/PauseMenu.gd"
}, {
"base": &"CharacterBody2D",
"class": &"Player",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/player/Player.gd"
}, {
"base": &"CharacterBody2D",
"class": &"Slime",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/enemies/Slime.gd"
}, {
"base": &"Control",
"class": &"TouchControls",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ui/TouchControls.gd"
}]
