extends Area2D
class_name HealthPotion

# Health potion power-up
signal collected

@export var heal_amount: int = 1
@export var bounce_height: float = 8.0
@export var bounce_speed: float = 3.0

@onready var sprite: Sprite2D = $Sprite2D
@onready var collision_shape: CollisionShape2D = $CollisionShape2D
@onready var collect_particles: CPUParticles2D = $CollectParticles

var is_collected: bool = false
var original_position: Vector2
var bounce_timer: float = 0.0

func _ready():
	# Set up collision layers
	collision_layer = 8  # Collectibles layer
	collision_mask = 0   # Don't collide with anything
	
	# Connect signals
	body_entered.connect(_on_body_entered)
	
	# Store original position for bouncing animation
	original_position = global_position
	
	# Set up particles
	if collect_particles:
		collect_particles.emitting = false

func _physics_process(delta):
	if not is_collected:
		# Gentle bouncing animation
		bounce_timer += delta * bounce_speed
		global_position.y = original_position.y + sin(bounce_timer) * bounce_height

func _on_body_entered(body):
	if is_collected:
		return
	
	if body is Player:
		collect_potion(body)

func collect_potion(player: Player):
	if is_collected:
		return
	
	is_collected = true
	
	# Disable collision
	collision_shape.disabled = true
	
	# Emit particles
	if collect_particles:
		collect_particles.emitting = true
	
	# Play sound effect
	if AudioManager:
		AudioManager.play_power_up_sound()
	
	# Trigger haptic feedback
	if UIManager:
		UIManager.trigger_haptic_feedback(0.5)
	
	# Heal the player
	if player.has_method("heal"):
		player.heal(heal_amount)
	
	# Update game manager
	if GameManager:
		GameManager.heal(heal_amount)
		GameManager.add_score(heal_amount * 25)
	
	# Emit signal
	collected.emit()
	
	# Animate collection effect
	animate_collection()

func animate_collection():
	# Scale up and fade out animation
	var tween = create_tween()
	tween.set_parallel(true)
	
	# Scale animation
	tween.tween_property(self, "scale", Vector2(1.5, 1.5), 0.4)
	
	# Fade out animation
	tween.tween_property(self, "modulate:a", 0.0, 0.4)
	
	# Move up slightly
	tween.tween_property(self, "global_position:y", global_position.y - 40, 0.4)
	
	# Wait for animation to complete
	await tween.finished
	
	# Remove the potion
	queue_free()

# For save/load functionality
func get_save_data() -> Dictionary:
	return {
		"position": global_position,
		"collected": is_collected,
		"heal_amount": heal_amount
	}

func load_save_data(data: Dictionary):
	if data.has("position"):
		global_position = data.position
		original_position = global_position
	
	if data.has("collected") and data.collected:
		# If already collected, remove immediately
		queue_free()
	
	if data.has("heal_amount"):
		heal_amount = data.heal_amount
