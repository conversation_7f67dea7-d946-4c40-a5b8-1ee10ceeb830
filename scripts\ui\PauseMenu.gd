extends Control
class_name PauseMenu

# Pause menu for the game
@onready var resume_button: Button = $Panel/VBox/ResumeButton
@onready var restart_button: Button = $Panel/VBox/RestartButton
@onready var main_menu_button: Button = $Panel/VBox/MainMenuButton
@onready var settings_button: Button = $Panel/VBox/SettingsButton
@onready var panel: Panel = $Panel

func _ready():
	# Initially hidden
	visible = false
	
	# Connect buttons
	resume_button.pressed.connect(_on_resume_pressed)
	restart_button.pressed.connect(_on_restart_pressed)
	main_menu_button.pressed.connect(_on_main_menu_pressed)
	settings_button.pressed.connect(_on_settings_pressed)
	
	# Register with UIManager
	if UIManager:
		UIManager.register_pause_menu(self)

func _on_resume_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	if GameManager:
		GameManager.resume_game()

func _on_restart_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	# Restart current level
	get_tree().paused = false
	get_tree().reload_current_scene()

func _on_main_menu_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	# Return to main menu
	get_tree().paused = false
	get_tree().change_scene_to_file("res://scenes/ui/MainMenu.tscn")

func _on_settings_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	# TODO: Open settings submenu
	pass

func show_menu():
	visible = true
	# Animate in
	panel.scale = Vector2.ZERO
	var tween = create_tween()
	tween.tween_property(panel, "scale", Vector2.ONE, 0.3)

func hide_menu():
	# Animate out
	var tween = create_tween()
	tween.tween_property(panel, "scale", Vector2.ZERO, 0.2)
	await tween.finished
	visible = false
