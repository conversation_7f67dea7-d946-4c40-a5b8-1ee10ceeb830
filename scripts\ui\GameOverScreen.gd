extends Control
class_name GameOverScreen

# Game over screen with restart and menu options
@onready var score_label: Label = $Panel/VBox/ScoreLabel
@onready var coins_label: Label = $Panel/VBox/CoinsLabel
@onready var restart_button: Button = $Panel/VBox/ButtonContainer/RestartButton
@onready var main_menu_button: Button = $Panel/VBox/ButtonContainer/MainMenuButton
@onready var panel: Panel = $Panel

var final_score: int = 0
var final_coins: int = 0

func _ready():
	# Initially hidden
	visible = false
	
	# Connect buttons
	restart_button.pressed.connect(_on_restart_pressed)
	main_menu_button.pressed.connect(_on_main_menu_pressed)
	
	# Register with UIManager
	if UIManager:
		UIManager.register_game_over_screen(self)

func show_game_over(score: int, coins: int):
	final_score = score
	final_coins = coins
	
	# Update labels
	score_label.text = "Final Score: " + str(score)
	coins_label.text = "Coins Collected: " + str(coins)
	
	# Show screen
	visible = true
	
	# Animate in
	panel.scale = Vector2.ZERO
	panel.modulate.a = 0.0
	
	var tween = create_tween()
	tween.set_parallel(true)
	tween.tween_property(panel, "scale", Vector2.ONE, 0.5)
	tween.tween_property(panel, "modulate:a", 1.0, 0.5)

func _on_restart_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	# Restart current level
	get_tree().reload_current_scene()

func _on_main_menu_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	# Return to main menu
	get_tree().change_scene_to_file("res://scenes/ui/MainMenu.tscn")

func hide_screen():
	visible = false
