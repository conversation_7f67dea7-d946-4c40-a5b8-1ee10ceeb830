extends Control
class_name MainMenu

# Main menu with level selection and settings
@onready var title_label: Label = $VBox/TitleLabel
@onready var play_button: Button = $VBox/ButtonContainer/PlayButton
@onready var level_select_button: Button = $VBox/ButtonContainer/LevelSelectButton
@onready var settings_button: Button = $VBox/ButtonContainer/SettingsButton
@onready var quit_button: Button = $VBox/ButtonContainer/QuitButton
@onready var level_select_panel: Control = $LevelSelectPanel
@onready var settings_panel: Control = $SettingsPanel
@onready var background: TextureRect = $Background

# Level selection
@onready var level_grid: GridContainer = $LevelSelectPanel/VBox/ScrollContainer/LevelGrid
@onready var back_button: Button = $LevelSelectPanel/VBox/BackButton

# Settings
@onready var music_slider: HSlider = $SettingsPanel/VBox/MusicContainer/MusicSlider
@onready var sfx_slider: HSlider = $SettingsPanel/VBox/SFXContainer/SFXSlider
@onready var haptic_checkbox: CheckBox = $SettingsPanel/VBox/HapticContainer/HapticCheckBox
@onready var settings_back_button: Button = $SettingsPanel/VBox/SettingsBackButton

# Animation
var menu_tween: Tween

func _ready():
	setup_menu()
	connect_signals()
	load_settings()
	create_level_buttons()
	
	# Start background music
	if AudioManager:
		AudioManager.play_menu_music()
	
	# Animate menu entrance
	animate_menu_entrance()

func setup_menu():
	# Hide panels initially
	level_select_panel.visible = false
	settings_panel.visible = false
	
	# Set up background
	if background:
		background.texture = load("res://assets/sprites/environment/background.png")

func connect_signals():
	# Main menu buttons
	play_button.pressed.connect(_on_play_pressed)
	level_select_button.pressed.connect(_on_level_select_pressed)
	settings_button.pressed.connect(_on_settings_pressed)
	quit_button.pressed.connect(_on_quit_pressed)
	
	# Level select
	back_button.pressed.connect(_on_back_pressed)
	
	# Settings
	music_slider.value_changed.connect(_on_music_changed)
	sfx_slider.value_changed.connect(_on_sfx_changed)
	haptic_checkbox.toggled.connect(_on_haptic_toggled)
	settings_back_button.pressed.connect(_on_settings_back_pressed)

func animate_menu_entrance():
	# Fade in and slide down animation
	modulate.a = 0.0
	position.y -= 50
	
	menu_tween = create_tween()
	menu_tween.set_parallel(true)
	menu_tween.tween_property(self, "modulate:a", 1.0, 0.5)
	menu_tween.tween_property(self, "position:y", position.y + 50, 0.5)

func _on_play_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	# Start first level or continue from last unlocked
	var level_to_load = 1
	if GameManager:
		level_to_load = GameManager.max_unlocked_level
	
	load_level(level_to_load)

func _on_level_select_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	show_level_select()

func _on_settings_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	show_settings()

func _on_quit_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	# On mobile, minimize the app instead of quitting
	if OS.get_name() in ["Android", "iOS"]:
		OS.request_permissions()
	else:
		get_tree().quit()

func show_level_select():
	# Animate transition to level select
	var tween = create_tween()
	tween.tween_property($VBox, "position:x", -get_viewport().size.x, 0.3)
	tween.tween_callback(func(): level_select_panel.visible = true)
	tween.tween_property(level_select_panel, "position:x", 0, 0.3)

func show_settings():
	# Animate transition to settings
	var tween = create_tween()
	tween.tween_property($VBox, "position:x", -get_viewport().size.x, 0.3)
	tween.tween_callback(func(): settings_panel.visible = true)
	tween.tween_property(settings_panel, "position:x", 0, 0.3)

func _on_back_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	hide_level_select()

func _on_settings_back_pressed():
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	hide_settings()

func hide_level_select():
	var tween = create_tween()
	tween.tween_property(level_select_panel, "position:x", get_viewport().size.x, 0.3)
	tween.tween_callback(func(): level_select_panel.visible = false)
	tween.tween_property($VBox, "position:x", 0, 0.3)

func hide_settings():
	var tween = create_tween()
	tween.tween_property(settings_panel, "position:x", get_viewport().size.x, 0.3)
	tween.tween_callback(func(): settings_panel.visible = false)
	tween.tween_property($VBox, "position:x", 0, 0.3)

func create_level_buttons():
	# Clear existing buttons
	for child in level_grid.get_children():
		child.queue_free()
	
	# Create level buttons
	var max_levels = 10  # Total number of levels
	for i in range(1, max_levels + 1):
		var level_button = Button.new()
		level_button.text = str(i)
		level_button.custom_minimum_size = Vector2(80, 80)
		
		# Check if level is unlocked
		var is_unlocked = GameManager and GameManager.is_level_unlocked(i)
		level_button.disabled = not is_unlocked
		
		if is_unlocked:
			level_button.pressed.connect(_on_level_button_pressed.bind(i))
		else:
			level_button.modulate = Color(0.5, 0.5, 0.5, 1.0)
		
		# Add completion indicator
		if SaveManager:
			var level_data = SaveManager.get_level_progress(i)
			if level_data.completed:
				level_button.text += "\n★"
		
		level_grid.add_child(level_button)

func _on_level_button_pressed(level_number: int):
	if AudioManager:
		AudioManager.play_button_click_sound()
	
	load_level(level_number)

func load_level(level_number: int):
	# Fade out menu
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.5)
	await tween.finished
	
	# Load the level scene
	var level_path = "res://scenes/levels/Level" + str(level_number) + ".tscn"
	if ResourceLoader.exists(level_path):
		get_tree().change_scene_to_file(level_path)
	else:
		# Fallback to test level
		get_tree().change_scene_to_file("res://scenes/levels/TestLevel.tscn")

func load_settings():
	if not SaveManager:
		return
	
	var settings = SaveManager.load_settings()
	
	# Update sliders
	music_slider.value = settings.get("music_volume", 0.7) * 100
	sfx_slider.value = settings.get("sfx_volume", 0.8) * 100
	haptic_checkbox.button_pressed = settings.get("haptic_feedback", true)

func _on_music_changed(value: float):
	if AudioManager:
		AudioManager.set_music_volume(value / 100.0)

func _on_sfx_changed(value: float):
	if AudioManager:
		AudioManager.set_sfx_volume(value / 100.0)
		# Play test sound
		AudioManager.play_button_click_sound()

func _on_haptic_toggled(enabled: bool):
	if SaveManager:
		var settings = SaveManager.load_settings()
		settings["haptic_feedback"] = enabled
		SaveManager.save_settings(settings)
	
	if UIManager:
		UIManager.haptic_feedback_enabled = enabled

func _input(event):
	# Handle back button on mobile
	if event.is_action_pressed("ui_cancel") or (event is InputEventKey and event.keycode == KEY_ESCAPE):
		if level_select_panel.visible:
			hide_level_select()
		elif settings_panel.visible:
			hide_settings()
		elif OS.get_name() in ["Android", "iOS"]:
			# Minimize app on mobile
			OS.request_permissions()

func update_level_buttons():
	# Refresh level button states
	create_level_buttons()
