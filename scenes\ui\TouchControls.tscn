[gd_scene load_steps=6 format=3 uid="uid://cqxvn8ywqxqxr"]

[ext_resource type="Script" path="res://scripts/ui/TouchControls.gd" id="1_touch"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(1, 1, 1, 0.3)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 1, 1, 0.6)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2"]
bg_color = Color(0.8, 0.8, 0.8, 0.5)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 1, 1, 0.8)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_3"]
bg_color = Color(1, 1, 1, 0.2)
border_width_left = 3
border_width_top = 3
border_width_right = 3
border_width_bottom = 3
border_color = Color(1, 1, 1, 0.4)
corner_radius_top_left = 50
corner_radius_top_right = 50
corner_radius_bottom_right = 50
corner_radius_bottom_left = 50

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4"]
bg_color = Color(1, 1, 1, 0.6)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 1, 1, 0.8)
corner_radius_top_left = 25
corner_radius_top_right = 25
corner_radius_bottom_right = 25
corner_radius_bottom_left = 25

[node name="TouchControls" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
script = ExtResource("1_touch")

[node name="LeftButton" type="TouchScreenButton" parent="."]
position = Vector2(50, 770)
scale = Vector2(1, 1)
texture_normal = SubResource("StyleBoxFlat_1")
texture_pressed = SubResource("StyleBoxFlat_2")
action = "move_left"
visibility_mode = 1

[node name="Label" type="Label" parent="LeftButton"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -10.0
offset_top = -12.0
offset_right = 10.0
offset_bottom = 12.0
text = "←"
horizontal_alignment = 1
vertical_alignment = 1

[node name="RightButton" type="TouchScreenButton" parent="."]
position = Vector2(150, 770)
texture_normal = SubResource("StyleBoxFlat_1")
texture_pressed = SubResource("StyleBoxFlat_2")
action = "move_right"
visibility_mode = 1

[node name="Label" type="Label" parent="RightButton"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -10.0
offset_top = -12.0
offset_right = 10.0
offset_bottom = 12.0
text = "→"
horizontal_alignment = 1
vertical_alignment = 1

[node name="JumpButton" type="TouchScreenButton" parent="."]
position = Vector2(930, 770)
texture_normal = SubResource("StyleBoxFlat_1")
texture_pressed = SubResource("StyleBoxFlat_2")
action = "jump"
visibility_mode = 1

[node name="Label" type="Label" parent="JumpButton"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -15.0
offset_top = -12.0
offset_right = 15.0
offset_bottom = 12.0
text = "JUMP"
horizontal_alignment = 1
vertical_alignment = 1

[node name="AttackButton" type="TouchScreenButton" parent="."]
position = Vector2(830, 770)
texture_normal = SubResource("StyleBoxFlat_1")
texture_pressed = SubResource("StyleBoxFlat_2")
action = "attack"
visibility_mode = 1

[node name="Label" type="Label" parent="AttackButton"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -12.0
offset_right = 20.0
offset_bottom = 12.0
text = "ATTACK"
horizontal_alignment = 1
vertical_alignment = 1

[node name="VirtualJoystick" type="Control" parent="."]
visible = false
layout_mode = 1
anchors_preset = 0
offset_left = 50.0
offset_top = 720.0
offset_right = 150.0
offset_bottom = 820.0
mouse_filter = 2

[node name="Base" type="Control" parent="VirtualJoystick"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -50.0
offset_top = -50.0
offset_right = 50.0
offset_bottom = 50.0
mouse_filter = 2

[node name="Background" type="Panel" parent="VirtualJoystick/Base"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_3")

[node name="Knob" type="Control" parent="VirtualJoystick"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -25.0
offset_top = -25.0
offset_right = 25.0
offset_bottom = 25.0
mouse_filter = 2

[node name="KnobPanel" type="Panel" parent="VirtualJoystick/Knob"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_4")
